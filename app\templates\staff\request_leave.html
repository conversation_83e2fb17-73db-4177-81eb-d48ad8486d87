{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-calendar-plus"></i> Request Leave</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('staff.leave_history') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-history"></i> Leave History
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-times"></i> Leave Request Form</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.leave_type.label(class="form-label") }}
                            {{ form.leave_type(class="form-select") }}
                            {% if form.leave_type.errors %}
                                <div class="text-danger">
                                    {% for error in form.leave_type.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.start_date.label(class="form-label") }}
                            {{ form.start_date(class="form-control") }}
                            {% if form.start_date.errors %}
                                <div class="text-danger">
                                    {% for error in form.start_date.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.end_date.label(class="form-label") }}
                            {{ form.end_date(class="form-control") }}
                            {% if form.end_date.errors %}
                                <div class="text-danger">
                                    {% for error in form.end_date.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Total Days</label>
                        <div class="form-control-plaintext" id="total-days">
                            <span class="text-muted">Select start and end dates to calculate</span>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.reason.label(class="form-label") }}
                        {{ form.reason(class="form-control", rows="4", placeholder="Please provide a detailed reason for your leave request...") }}
                        {% if form.reason.errors %}
                            <div class="text-danger">
                                {% for error in form.reason.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                        <small class="form-text text-muted">Minimum 10 characters required</small>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('staff.dashboard') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Leave Policy</h5>
            </div>
            <div class="card-body">
                <h6>Leave Types:</h6>
                <ul class="small">
                    <li><strong>Sick Leave:</strong> For medical reasons</li>
                    <li><strong>Casual Leave:</strong> For personal matters</li>
                    <li><strong>Annual Leave:</strong> Vacation time</li>
                    <li><strong>Maternity Leave:</strong> For new mothers</li>
                    <li><strong>Paternity Leave:</strong> For new fathers</li>
                    <li><strong>Emergency Leave:</strong> For urgent situations</li>
                </ul>
                
                <hr>
                
                <h6>Important Notes:</h6>
                <ul class="small">
                    <li>Submit requests at least 3 days in advance</li>
                    <li>Emergency leaves can be submitted same day</li>
                    <li>Provide detailed reason for approval</li>
                    <li>Check with your supervisor for urgent requests</li>
                </ul>
                
                <hr>
                
                <div class="alert alert-info small">
                    <i class="fas fa-clock"></i>
                    <strong>Processing Time:</strong> Leave requests are typically processed within 24-48 hours.
                </div>
            </div>
        </div>
        
        <!-- Leave Balance (if available) -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-pie"></i> Leave Balance</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="text-success">15</h4>
                            <small class="text-muted">Available</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="text-warning">5</h4>
                        <small class="text-muted">Used</small>
                    </div>
                </div>
                <hr>
                <div class="progress" style="height: 10px;">
                    <div class="progress-bar bg-warning" role="progressbar" style="width: 25%" aria-valuenow="25" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <small class="text-muted">25% of annual leave used</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const startDateField = document.getElementById('start_date');
    const endDateField = document.getElementById('end_date');
    const totalDaysDisplay = document.getElementById('total-days');
    
    function calculateDays() {
        const startDate = startDateField.value;
        const endDate = endDateField.value;
        
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            
            if (end >= start) {
                const timeDiff = end.getTime() - start.getTime();
                const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24)) + 1; // +1 to include both start and end dates
                
                totalDaysDisplay.innerHTML = `<strong class="text-primary">${daysDiff} day${daysDiff !== 1 ? 's' : ''}</strong>`;
            } else {
                totalDaysDisplay.innerHTML = '<span class="text-danger">End date must be after start date</span>';
            }
        } else {
            totalDaysDisplay.innerHTML = '<span class="text-muted">Select start and end dates to calculate</span>';
        }
    }
    
    startDateField.addEventListener('change', calculateDays);
    endDateField.addEventListener('change', calculateDays);
    
    // Set minimum date to today
    const today = new Date().toISOString().split('T')[0];
    startDateField.setAttribute('min', today);
    endDateField.setAttribute('min', today);
    
    // Update end date minimum when start date changes
    startDateField.addEventListener('change', function() {
        endDateField.setAttribute('min', this.value);
        if (endDateField.value && endDateField.value < this.value) {
            endDateField.value = this.value;
        }
        calculateDays();
    });
});
</script>
{% endblock %}
