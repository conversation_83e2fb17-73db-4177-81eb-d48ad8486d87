{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-users"></i> Staff Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.register_staff') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-user-plus"></i> Register New Staff
            </a>
        </div>
    </div>
</div>

<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="text" name="search" class="form-control me-2" placeholder="Search by name, ID, or email..." value="{{ search }}">
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-search"></i>
            </button>
        </form>
    </div>
    <div class="col-md-6 text-end">
        <span class="text-muted">
            Showing {{ staff_members.items|length }} of {{ staff_members.total }} staff members
        </span>
    </div>
</div>

<!-- Staff List -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list"></i> Staff List</h5>
    </div>
    <div class="card-body p-0">
        {% if staff_members.items %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Staff ID</th>
                            <th>Name</th>
                            <th>Email</th>
                            <th>Department</th>
                            <th>Designation</th>
                            <th>Joining Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for staff in staff_members.items %}
                        <tr>
                            <td>
                                <strong>{{ staff.staff_id }}</strong>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                        {{ staff.first_name[0] }}{{ staff.last_name[0] }}
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ staff.full_name }}</div>
                                        {% if staff.phone %}
                                            <small class="text-muted">{{ staff.phone }}</small>
                                        {% endif %}
                                    </div>
                                </div>
                            </td>
                            <td>{{ staff.email }}</td>
                            <td>
                                {% if staff.department %}
                                    <span class="badge bg-info">{{ staff.department }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>{{ staff.designation or '-' }}</td>
                            <td>{{ staff.date_of_joining.strftime('%m/%d/%Y') if staff.date_of_joining else '-' }}</td>
                            <td>
                                {% if staff.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('admin.staff_detail', staff_id=staff.id) }}" 
                                       class="btn btn-sm btn-outline-primary" title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-success" 
                                            title="Register Fingerprint" onclick="registerFingerprint({{ staff.id }})">
                                        <i class="fas fa-fingerprint"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                            title="Edit Staff" onclick="editStaff({{ staff.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No staff members found</h5>
                {% if search %}
                    <p class="text-muted">No results for "{{ search }}"</p>
                    <a href="{{ url_for('admin.staff_list') }}" class="btn btn-outline-primary">
                        <i class="fas fa-times"></i> Clear Search
                    </a>
                {% else %}
                    <p class="text-muted">Start by registering your first staff member.</p>
                    <a href="{{ url_for('admin.register_staff') }}" class="btn btn-primary">
                        <i class="fas fa-user-plus"></i> Register First Staff
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if staff_members.pages > 1 %}
<nav aria-label="Staff pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if staff_members.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('admin.staff_list', page=staff_members.prev_num, search=search) }}">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
            </li>
        {% endif %}
        
        {% for page_num in staff_members.iter_pages() %}
            {% if page_num %}
                {% if page_num != staff_members.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.staff_list', page=page_num, search=search) }}">
                            {{ page_num }}
                        </a>
                    </li>
                {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if staff_members.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('admin.staff_list', page=staff_members.next_num, search=search) }}">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function registerFingerprint(staffId) {
    // Placeholder for fingerprint registration
    alert('Fingerprint registration for staff ID: ' + staffId + '\n\nThis would open the biometric device interface.');
}

function editStaff(staffId) {
    // Placeholder for edit functionality
    alert('Edit staff functionality for ID: ' + staffId + '\n\nThis would open the edit form.');
}
</script>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    font-weight: bold;
}
</style>
{% endblock %}
