from flask_wtf import <PERSON>laskForm
from wtforms import <PERSON><PERSON><PERSON>, <PERSON>wordField, BooleanField, SubmitField, <PERSON>Field
from wtforms.validators import <PERSON>Required, Email, EqualTo, Length, ValidationError
from app.models import User

class LoginForm(FlaskForm):
    username = <PERSON><PERSON>ield('Username', validators=[DataRequired()])
    password = PasswordField('Password', validators=[DataRequired()])
    remember_me = BooleanField('Remember Me')
    submit = SubmitField('Sign In')

class RegistrationForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=4, max=20)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    password = PasswordField('Password', validators=[DataRequired(), Length(min=6)])
    password2 = PasswordField(
        'Repeat Password', 
        validators=[DataRequired(), EqualTo('password')]
    )
    role = SelectField('Role', choices=[('staff', 'Staff'), ('admin', 'Admin')], default='staff')
    submit = SubmitField('Register')
    
    def validate_username(self, username):
        user = User.query.filter_by(username=username.data).first()
        if user is not None:
            raise ValidationError('Please use a different username.')
    
    def validate_email(self, email):
        user = User.query.filter_by(email=email.data).first()
        if user is not None:
            raise ValidationError('Please use a different email address.')

class ChangePasswordForm(FlaskForm):
    current_password = PasswordField('Current Password', validators=[DataRequired()])
    new_password = PasswordField('New Password', validators=[DataRequired(), Length(min=6)])
    new_password2 = PasswordField(
        'Repeat New Password',
        validators=[DataRequired(), EqualTo('new_password')]
    )
    submit = SubmitField('Change Password')
