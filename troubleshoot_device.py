#!/usr/bin/env python3
"""
Device Troubleshooting Script
Automatically diagnoses device connection issues
"""

import socket
import os
from datetime import datetime

def print_banner():
    """Print troubleshooting banner"""
    print("=" * 70)
    print("🔧 ESSL Device Connection Troubleshooter")
    print("=" * 70)
    print(f"⏰ Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def check_current_config():
    """Check current device configuration"""
    print("📋 Current Configuration:")
    
    config = {}
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if line.startswith('ESSL_DEVICE'):
                    key, value = line.strip().split('=', 1)
                    config[key] = value
                    print(f"   {key} = {value}")
    else:
        print("   ⚠️ No .env file found")
    
    return config

def get_network_info():
    """Get local network information"""
    print("\n🌐 Network Information:")
    
    try:
        # Get local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        
        print(f"   📍 Your computer IP: {local_ip}")
        
        # Suggest network range
        network_base = '.'.join(local_ip.split('.')[:-1])
        print(f"   🌐 Your network range: {network_base}.1-254")
        print(f"   💡 Device should be in range: {network_base}.100-200")
        
        return local_ip, network_base
        
    except Exception as e:
        print(f"   ⚠️ Could not determine network info: {str(e)}")
        return None, "192.168.1"

def test_device_connection(ip, port=4370, timeout=3):
    """Test connection to a specific device"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip, port))
        sock.close()
        
        if result == 0:
            return True, "Connection successful"
        else:
            return False, f"Connection refused (error {result})"
            
    except socket.timeout:
        return False, "Connection timeout"
    except socket.gaierror:
        return False, "DNS resolution failed"
    except Exception as e:
        return False, str(e)

def scan_network_range(network_base, start=100, end=110):
    """Scan a small network range for devices"""
    print(f"\n🔍 Quick scan of {network_base}.{start}-{end}...")
    
    found_devices = []
    
    for i in range(start, end + 1):
        ip = f"{network_base}.{i}"
        success, message = test_device_connection(ip, timeout=1)
        
        if success:
            print(f"   ✅ Device found at {ip}")
            found_devices.append(ip)
        else:
            print(f"   ❌ {ip}: {message}")
    
    return found_devices

def diagnose_connection_issue(config_ip, config_port):
    """Diagnose specific connection issues"""
    print(f"\n🔍 Diagnosing connection to {config_ip}:{config_port}...")
    
    # Test 1: Basic network connectivity
    print("   Test 1: Basic network connectivity")
    success, message = test_device_connection(config_ip, config_port, timeout=5)
    
    if success:
        print(f"      ✅ {message}")
        return True
    else:
        print(f"      ❌ {message}")
    
    # Test 2: Ping test
    print("   Test 2: Ping test")
    ping_result = os.system(f"ping -n 1 -w 1000 {config_ip} >nul 2>&1")
    if ping_result == 0:
        print(f"      ✅ Ping successful to {config_ip}")
    else:
        print(f"      ❌ Ping failed to {config_ip}")
    
    # Test 3: Different ports
    print("   Test 3: Testing different ports")
    common_ports = [4370, 8080, 80, 443, 23, 8000]
    
    for port in common_ports:
        success, message = test_device_connection(config_ip, port, timeout=1)
        if success:
            print(f"      ✅ Port {port} is open on {config_ip}")
        else:
            print(f"      ❌ Port {port}: {message}")
    
    return False

def provide_solutions(config, local_ip, network_base):
    """Provide troubleshooting solutions"""
    print("\n💡 Troubleshooting Solutions:")
    print("=" * 30)
    
    config_ip = config.get('ESSL_DEVICE_IP', '*************')
    
    print("1. 🔌 Physical Connection:")
    print("   - Ensure device is powered on")
    print("   - Check Ethernet cable connection")
    print("   - Verify network switch/router is working")
    print()
    
    print("2. 📱 Device Configuration:")
    print("   - Check device IP in menu: Menu → Comm → Ethernet")
    print("   - Ensure device IP is in your network range")
    print(f"   - Your network: {network_base}.1-254")
    print(f"   - Suggested device IP: {network_base}.100")
    print()
    
    print("3. 🌐 Network Settings:")
    print(f"   - Your computer IP: {local_ip}")
    print(f"   - Device should be: {network_base}.100-200")
    print("   - Use static IP on device (recommended)")
    print("   - Check subnet mask: *************")
    print()
    
    print("4. 🛡️ Firewall/Security:")
    print("   - Allow port 4370 in Windows Firewall")
    print("   - Disable antivirus temporarily for testing")
    print("   - Check router firewall settings")
    print()
    
    print("5. 🔧 Configuration Fix:")
    print("   - Update .env file with correct device IP")
    print("   - Example:")
    print(f"     ESSL_DEVICE_IP={network_base}.100")
    print("     ESSL_DEVICE_PORT=4370")

def create_fixed_config(network_base):
    """Create a fixed configuration file"""
    print(f"\n📝 Creating updated configuration...")
    
    suggested_ip = f"{network_base}.100"
    
    env_content = f"""# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-{datetime.now().strftime('%Y%m%d')}

# Database Configuration
DATABASE_URL=sqlite:///staff_management.db

# ESSL X990+ID Biometric Device Configuration
# Update the IP address below to match your device
ESSL_DEVICE_IP={suggested_ip}
ESSL_DEVICE_PORT=4370
ESSL_DEVICE_PASSWORD=0

# Multiple Device Support
ESSL_DEVICE_LIST={suggested_ip}:4370
ESSL_DEVICE_COUNT=1

# Auto-Detection Settings
AUTO_DETECT_DEVICES=true
DEVICE_SCAN_INTERVAL=300
DEVICE_TIMEOUT=5

# Email Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# Configuration updated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Suggested device IP: {suggested_ip}
# Please verify device IP in device menu and update above if different
"""
    
    # Backup existing config
    if os.path.exists('.env'):
        backup_name = f'.env.backup.{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        os.rename('.env', backup_name)
        print(f"   📋 Backed up existing config to {backup_name}")
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print(f"   ✅ Created new configuration with suggested IP: {suggested_ip}")
    print(f"   ⚠️ Please verify device IP and update .env file if different")

def main():
    """Main troubleshooting function"""
    print_banner()
    
    # Check current configuration
    config = check_current_config()
    
    # Get network information
    local_ip, network_base = get_network_info()
    
    # Test current configuration
    if config.get('ESSL_DEVICE_IP'):
        config_ip = config['ESSL_DEVICE_IP']
        config_port = int(config.get('ESSL_DEVICE_PORT', 4370))
        
        # Diagnose connection issue
        if not diagnose_connection_issue(config_ip, config_port):
            # Quick scan for devices
            found_devices = scan_network_range(network_base)
            
            if found_devices:
                print(f"\n🎉 Found {len(found_devices)} device(s) on network:")
                for device in found_devices:
                    print(f"   📱 {device}")
                print(f"\n💡 Update your .env file:")
                print(f"   ESSL_DEVICE_IP={found_devices[0]}")
            else:
                print(f"\n❌ No devices found in quick scan")
    
    # Provide solutions
    provide_solutions(config, local_ip, network_base)
    
    # Create fixed configuration
    create_fixed_config(network_base)
    
    print(f"\n🚀 Next Steps:")
    print("1. Check your device IP in device menu")
    print("2. Update .env file with correct IP if needed")
    print("3. Restart the application: python app.py")
    print("4. Test connection in Admin → Device Management")

if __name__ == '__main__':
    main()
