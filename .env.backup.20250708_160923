# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-20250708

# Database Configuration
DATABASE_URL=sqlite:///staff_management.db

# ESSL X990+ID Biometric Device Configuration
# Update the IP address below to match your device
ESSL_DEVICE_IP=**************
ESSL_DEVICE_PORT=4370
ESSL_DEVICE_PASSWORD=0

# Multiple Device Support
ESSL_DEVICE_LIST=**************:4370
ESSL_DEVICE_COUNT=1

# Auto-Detection Settings
AUTO_DETECT_DEVICES=true
DEVICE_SCAN_INTERVAL=300
DEVICE_TIMEOUT=5

# Email Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# Configuration updated on: 2025-07-08 16:06:58
# Suggested device IP: **************
# Please verify device IP in device menu and update above if different
