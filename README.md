# Staff Management System with Biometric Integration

A comprehensive web-based staff management system with ESSL X990+ID biometric attendance integration built with Flask.

## Features

### 🔐 Authentication & Authorization
- Role-based access control (Admin/Staff)
- Secure login/logout functionality
- Password change capability

### 👥 Staff Management
- Staff registration with detailed information
- Fingerprint template mapping
- Employee profile management
- Department and designation tracking

### 📅 Attendance Management
- Real-time biometric attendance tracking
- Manual attendance entry (Admin)
- Calendar view of attendance
- In/Out time tracking with break management
- Overtime calculation

### 🏖️ Leave Management
- Staff leave request submission
- Admin approval/rejection workflow
- Leave history tracking
- Multiple leave types support

### 📊 Reporting & Analytics
- Attendance reports (PDF/Excel)
- Leave summaries
- Working hours tracking
- Dashboard with key metrics

### 🔧 Biometric Integration
- ESSL X990+ID device support
- Real-time data synchronization
- Fingerprint registration
- Device communication via SDK

## Technology Stack

- **Backend**: Python Flask
- **Database**: SQLAlchemy (MySQL/PostgreSQL/SQLite)
- **Frontend**: HTML, CSS, Bootstrap 5, JavaScript
- **Authentication**: Flask-Login
- **Forms**: Flask-WTF
- **Biometric**: ESSL SDK Integration

## Installation & Setup

### Prerequisites
- Python 3.8+
- MySQL or PostgreSQL (optional, SQLite works for development)
- ESSL X990+ID biometric device (for production)

### 1. Clone the Repository
```bash
git clone <repository-url>
cd staff-management
```

### 2. Create Virtual Environment
```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Configure Environment
```bash
cp .env.example .env
# Edit .env file with your configuration
```

### 5. Initialize Database
```bash
python init_db.py
```

### 6. Run the Application
```bash
python app.py
```

The application will be available at `http://localhost:5000`

## Default Login Credentials

- **Username**: admin
- **Password**: admin123
- **Role**: Administrator

## Configuration

### Environment Variables (.env)

```env
# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=mysql+pymysql://username:password@localhost/staff_management

# ESSL Device Configuration
ESSL_DEVICE_IP=*************
ESSL_DEVICE_PORT=4370
ESSL_DEVICE_PASSWORD=0

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

## API Endpoints

### Attendance Push API
```http
POST /api/attendance/push
Content-Type: application/json

{
    "device_user_id": 123,
    "timestamp": "2023-12-01 09:00:00",
    "punch_type": "in",
    "device_id": "ESSL_001"
}
```

### Bulk Attendance Push
```http
POST /api/attendance/bulk-push
Content-Type: application/json

{
    "records": [
        {
            "device_user_id": 123,
            "timestamp": "2023-12-01 09:00:00",
            "punch_type": "in"
        }
    ]
}
```

### Get Staff Attendance
```http
GET /api/staff/{staff_id}/attendance?start_date=2023-12-01&end_date=2023-12-31
```

## Database Schema

### Key Tables
- **users**: Authentication and user roles
- **staff**: Employee information and profiles
- **attendance**: Daily attendance records
- **leave_request**: Leave applications and approvals
- **fingerprint_template**: Biometric templates
- **attendance_log**: Raw device logs

## Biometric Device Integration

### ESSL X990+ID Setup
1. Connect device to network
2. Configure device IP in .env file
3. Register staff fingerprints via admin panel
4. Device will automatically sync attendance data

### Supported Operations
- Fingerprint registration
- Attendance log retrieval
- User management on device
- Real-time data synchronization

## Usage Guide

### For Administrators
1. **Staff Registration**
   - Navigate to Admin → Register Staff
   - Fill in employee details
   - Register fingerprints via biometric device

2. **Attendance Management**
   - View real-time attendance on dashboard
   - Manual entry for corrections
   - Generate reports

3. **Leave Management**
   - Review pending leave requests
   - Approve/reject with comments

### For Staff
1. **Attendance Tracking**
   - Use biometric device for check-in/out
   - View attendance history
   - Track working hours

2. **Leave Requests**
   - Submit leave applications
   - Track request status
   - View leave balance

## Development

### Project Structure
```
staff-management/
├── app/
│   ├── admin/          # Admin blueprint
│   ├── auth/           # Authentication
│   ├── main/           # Main routes
│   ├── staff/          # Staff portal
│   ├── api/            # API endpoints
│   ├── services/       # Business logic
│   ├── templates/      # HTML templates
│   └── models.py       # Database models
├── config.py           # Configuration
├── app.py             # Application entry point
├── init_db.py         # Database initialization
└── requirements.txt   # Dependencies
```

### Adding New Features
1. Create new blueprint in `app/`
2. Define routes and forms
3. Create templates
4. Update navigation in `base.html`
5. Add tests

## Testing

```bash
# Run tests
python -m pytest

# Run with coverage
python -m pytest --cov=app
```

## Deployment

### Production Considerations
- Use PostgreSQL or MySQL for production
- Configure proper secret keys
- Set up SSL/HTTPS
- Use production WSGI server (Gunicorn)
- Configure firewall for biometric device communication
- Set up automated backups

### Docker Deployment
```bash
# Build image
docker build -t staff-management .

# Run container
docker run -p 5000:5000 staff-management
```

## Troubleshooting

### Common Issues
1. **Database Connection**: Check DATABASE_URL in .env
2. **Biometric Device**: Verify IP and network connectivity
3. **Permissions**: Ensure proper file permissions for uploads
4. **Dependencies**: Update pip and reinstall requirements

### Logs
- Application logs: Check Flask debug output
- Device logs: Monitor biometric device communication
- Database logs: Check SQLAlchemy query logs

## Contributing

1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Submit pull request

## License

This project is licensed under the MIT License.

## Support

For support and questions:
- Create an issue on GitHub
- Check documentation
- Review troubleshooting guide
