{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-calendar-check"></i> My Attendance History</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-download"></i> Export
            </button>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="row mb-4">
    <div class="col-md-6">
        <form method="GET" class="d-flex">
            <input type="month" name="month" class="form-control me-2" value="{{ month }}">
            <button type="submit" class="btn btn-outline-primary">
                <i class="fas fa-filter"></i> Filter
            </button>
        </form>
    </div>
    <div class="col-md-6 text-end">
        <span class="text-muted">
            Showing {{ attendance_records.items|length }} records
        </span>
    </div>
</div>

<!-- Attendance Summary -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-left-primary shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Days
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ attendance_records.total }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-success shadow h-100 py-2 card-stats success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Present Days
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ attendance_records.items | selectattr('status', 'equalto', 'present') | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-warning shadow h-100 py-2 card-stats warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Late Days
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ attendance_records.items | selectattr('status', 'equalto', 'late') | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-info shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Total Hours
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ "%.1f"|format(attendance_records.items | sum(attribute='total_hours') or 0) }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-business-time fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Records -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list"></i> Attendance Records</h5>
    </div>
    <div class="card-body p-0">
        {% if attendance_records.items %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Date</th>
                            <th>Day</th>
                            <th>Check In</th>
                            <th>Check Out</th>
                            <th>Break Duration</th>
                            <th>Total Hours</th>
                            <th>Overtime</th>
                            <th>Status</th>
                            <th>Notes</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for attendance in attendance_records.items %}
                        <tr>
                            <td>
                                <strong>{{ attendance.date.strftime('%m/%d/%Y') }}</strong>
                            </td>
                            <td>
                                <small class="text-muted">{{ attendance.date.strftime('%A') }}</small>
                            </td>
                            <td>
                                {% if attendance.check_in_time %}
                                    <span class="text-success">
                                        <i class="fas fa-sign-in-alt"></i>
                                        {{ attendance.check_in_time.strftime('%H:%M') }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if attendance.check_out_time %}
                                    <span class="text-warning">
                                        <i class="fas fa-sign-out-alt"></i>
                                        {{ attendance.check_out_time.strftime('%H:%M') }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if attendance.break_start_time and attendance.break_end_time %}
                                    {% set break_duration = (attendance.break_end_time - attendance.break_start_time).total_seconds() / 3600 %}
                                    <small>{{ "%.1f"|format(break_duration) }}h</small>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <strong class="text-primary">
                                    {{ "%.2f"|format(attendance.total_hours) if attendance.total_hours else '0.00' }}h
                                </strong>
                            </td>
                            <td>
                                {% if attendance.overtime_hours and attendance.overtime_hours > 0 %}
                                    <span class="text-info">
                                        <i class="fas fa-plus-circle"></i>
                                        {{ "%.2f"|format(attendance.overtime_hours) }}h
                                    </span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if attendance.status == 'present' else 'warning' if attendance.status == 'late' else 'danger' if attendance.status == 'absent' else 'info' }}">
                                    {{ attendance.status.replace('_', ' ').title() }}
                                </span>
                                {% if attendance.is_manual_entry %}
                                    <br><small class="badge bg-secondary mt-1">Manual</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if attendance.notes %}
                                    <small class="text-muted">{{ attendance.notes[:50] }}{% if attendance.notes|length > 50 %}...{% endif %}</small>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No attendance records found</h5>
                {% if month %}
                    <p class="text-muted">No records for the selected month.</p>
                    <a href="{{ url_for('staff.attendance_history') }}" class="btn btn-outline-primary">
                        <i class="fas fa-calendar"></i> View All Records
                    </a>
                {% else %}
                    <p class="text-muted">Your attendance records will appear here once you start using the biometric system.</p>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if attendance_records.pages > 1 %}
<nav aria-label="Attendance pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if attendance_records.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('staff.attendance_history', page=attendance_records.prev_num, month=month) }}">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
            </li>
        {% endif %}
        
        {% for page_num in attendance_records.iter_pages() %}
            {% if page_num %}
                {% if page_num != attendance_records.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('staff.attendance_history', page=page_num, month=month) }}">
                            {{ page_num }}
                        </a>
                    </li>
                {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if attendance_records.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('staff.attendance_history', page=attendance_records.next_num, month=month) }}">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}
