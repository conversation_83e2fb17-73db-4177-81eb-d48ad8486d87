#!/usr/bin/env python3
"""
Device Configuration Helper
Helps configure the correct device connection settings
"""

import os
import socket
import time
from datetime import datetime

def print_banner():
    """Print configuration banner"""
    print("=" * 70)
    print("🔧 ESSL Device Configuration Helper")
    print("=" * 70)
    print(f"⏰ Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def check_current_config():
    """Check current device configuration"""
    print("📋 Current Configuration:")
    
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if line.startswith('ESSL_DEVICE'):
                    print(f"   {line.strip()}")
    else:
        print("   ⚠️ No .env file found")
    print()

def test_device_connection(ip, port=4370, timeout=3):
    """Test connection to a specific device"""
    print(f"🔍 Testing connection to {ip}:{port}...")
    
    try:
        # Test basic network connectivity first
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip, port))
        sock.close()
        
        if result == 0:
            print(f"   ✅ Network connection successful to {ip}:{port}")
            return True
        else:
            print(f"   ❌ Cannot connect to {ip}:{port}")
            return False
            
    except Exception as e:
        print(f"   ❌ Connection error: {str(e)}")
        return False

def scan_common_ips():
    """Scan common device IP addresses"""
    print("🔍 Scanning common device IP addresses...")
    
    common_ips = [
        "*************", "*************", "*************", "*************",
        "*************", "*************", "*************", "*************",
        "**********", "**********", "************", "************"
    ]
    
    found_devices = []
    
    for ip in common_ips:
        if test_device_connection(ip, timeout=2):
            found_devices.append(ip)
    
    return found_devices

def get_network_info():
    """Get local network information"""
    print("🌐 Network Information:")
    
    try:
        # Get local IP
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        
        print(f"   📍 Your computer IP: {local_ip}")
        
        # Suggest network range
        network_base = '.'.join(local_ip.split('.')[:-1])
        print(f"   🌐 Your network range: {network_base}.1-254")
        print(f"   💡 Device should be in range: {network_base}.100-200")
        
        return network_base
        
    except Exception as e:
        print(f"   ⚠️ Could not determine network info: {str(e)}")
        return "192.168.1"

def create_env_config(device_ip, device_port=4370):
    """Create .env configuration file"""
    print(f"📝 Creating configuration for device {device_ip}:{device_port}...")
    
    env_content = f"""# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-{datetime.now().strftime('%Y%m%d')}

# Database Configuration
DATABASE_URL=sqlite:///staff_management.db

# ESSL X990+ID Biometric Device Configuration
ESSL_DEVICE_IP={device_ip}
ESSL_DEVICE_PORT={device_port}
ESSL_DEVICE_PASSWORD=0

# Multiple Device Support
ESSL_DEVICE_LIST={device_ip}:{device_port}
ESSL_DEVICE_COUNT=1

# Auto-Detection Settings
AUTO_DETECT_DEVICES=true
DEVICE_SCAN_INTERVAL=300
DEVICE_TIMEOUT=5

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# Configuration created on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Device: {device_ip}:{device_port}
"""
    
    with open('.env', 'w') as f:
        f.write(env_content)
    
    print(f"✅ Configuration saved to .env file")

def interactive_setup():
    """Interactive device setup"""
    print("🎯 Interactive Device Setup")
    print("=" * 30)
    
    # Option 1: Manual IP entry
    print("\n1️⃣ Manual Device Configuration")
    manual_ip = input("Enter device IP address (or press Enter to skip): ").strip()
    
    if manual_ip:
        port_input = input(f"Enter port for {manual_ip} (default 4370): ").strip()
        port = int(port_input) if port_input else 4370
        
        if test_device_connection(manual_ip, port):
            create_env_config(manual_ip, port)
            return True
        else:
            print(f"❌ Could not connect to {manual_ip}:{port}")
    
    # Option 2: Auto-scan
    print("\n2️⃣ Automatic Device Discovery")
    scan_choice = input("Scan for devices automatically? (y/n): ").strip().lower()
    
    if scan_choice == 'y':
        found_devices = scan_common_ips()
        
        if found_devices:
            print(f"\n✅ Found {len(found_devices)} device(s):")
            for i, ip in enumerate(found_devices, 1):
                print(f"   {i}. {ip}")
            
            if len(found_devices) == 1:
                create_env_config(found_devices[0])
                return True
            else:
                choice = input(f"\nSelect device (1-{len(found_devices)}): ").strip()
                try:
                    idx = int(choice) - 1
                    if 0 <= idx < len(found_devices):
                        create_env_config(found_devices[idx])
                        return True
                except ValueError:
                    pass
        else:
            print("❌ No devices found during scan")
    
    # Option 3: No device setup
    print("\n3️⃣ Skip Device Configuration")
    print("You can configure devices later in the admin panel")
    
    skip_choice = input("Continue without device configuration? (y/n): ").strip().lower()
    if skip_choice == 'y':
        create_env_config("*************")  # Default config
        print("⚠️ Created default configuration - update later in admin panel")
        return True
    
    return False

def main():
    """Main configuration function"""
    print_banner()
    
    # Check current configuration
    check_current_config()
    
    # Get network information
    network_base = get_network_info()
    print()
    
    # Show device setup instructions
    print("📖 Device Setup Instructions:")
    print("=" * 30)
    print("1. 🔌 Connect your ESSL X990+ID device to power")
    print("2. 🌐 Connect device to your network (Ethernet cable)")
    print("3. 📱 Check device IP in device menu: Menu → Comm → Ethernet")
    print("4. 🖥️ Ensure device and computer are on same network")
    print("5. 🔧 Configure device IP if needed (static IP recommended)")
    print()
    
    # Interactive setup
    if interactive_setup():
        print("\n🎉 Configuration completed successfully!")
        print("\n🚀 Next steps:")
        print("1. Run: python app.py")
        print("2. Open: http://localhost:5000")
        print("3. Login: admin / admin123")
        print("4. Go to: Admin → Device Management")
        print("5. Test device connection")
        print("6. Register staff fingerprints")
    else:
        print("\n❌ Configuration incomplete")
        print("Please check device connection and try again")

if __name__ == '__main__':
    main()
