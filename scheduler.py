#!/usr/bin/env python3
"""
Background scheduler for automatic attendance synchronization
"""

import time
import logging
from datetime import datetime
from apscheduler.schedulers.blocking import BlockingScheduler
from apscheduler.triggers.cron import CronTrigger
from app import create_app
from app.services.biometric_service import BiometricService
from app.api.routes import process_attendance_log
from app.models import AttendanceLog

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('scheduler.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

def sync_attendance_job():
    """Job to sync attendance from biometric device"""
    try:
        logger.info("Starting attendance synchronization...")
        
        app = create_app()
        with app.app_context():
            # Sync attendance from device
            count = BiometricService.sync_attendance_from_device()
            logger.info(f"Synced {count} new attendance records")
            
            # Process any unprocessed logs
            unprocessed_logs = AttendanceLog.query.filter_by(processed=False).all()
            processed_count = 0
            
            for log in unprocessed_logs:
                try:
                    process_attendance_log(log)
                    processed_count += 1
                except Exception as e:
                    logger.error(f"Error processing log {log.id}: {str(e)}")
            
            logger.info(f"Processed {processed_count} unprocessed logs")
            
    except Exception as e:
        logger.error(f"Error in attendance sync job: {str(e)}")

def cleanup_old_logs_job():
    """Job to cleanup old attendance logs (older than 30 days)"""
    try:
        logger.info("Starting cleanup of old logs...")
        
        app = create_app()
        with app.app_context():
            from datetime import timedelta
            from app import db
            
            cutoff_date = datetime.utcnow() - timedelta(days=30)
            
            # Delete old processed logs
            deleted_count = AttendanceLog.query.filter(
                AttendanceLog.created_at < cutoff_date,
                AttendanceLog.processed == True
            ).delete()
            
            db.session.commit()
            logger.info(f"Cleaned up {deleted_count} old log entries")
            
    except Exception as e:
        logger.error(f"Error in cleanup job: {str(e)}")

def generate_daily_reports_job():
    """Job to generate daily attendance reports"""
    try:
        logger.info("Generating daily reports...")
        
        app = create_app()
        with app.app_context():
            from app.models import Staff, Attendance
            from datetime import date
            
            today = date.today()
            
            # Get today's attendance summary
            total_staff = Staff.query.filter_by(is_active=True).count()
            present_count = Attendance.query.filter_by(date=today, status='present').count()
            absent_count = Attendance.query.filter_by(date=today, status='absent').count()
            late_count = Attendance.query.filter_by(date=today, status='late').count()
            
            logger.info(f"Daily Summary for {today}:")
            logger.info(f"  Total Staff: {total_staff}")
            logger.info(f"  Present: {present_count}")
            logger.info(f"  Absent: {absent_count}")
            logger.info(f"  Late: {late_count}")
            
            # Here you could add email notifications or save reports to files
            
    except Exception as e:
        logger.error(f"Error in daily reports job: {str(e)}")

def main():
    """Main scheduler function"""
    logger.info("Starting Staff Management System Scheduler...")
    
    scheduler = BlockingScheduler()
    
    # Add jobs
    
    # Sync attendance every 5 minutes during working hours (8 AM to 8 PM)
    scheduler.add_job(
        sync_attendance_job,
        CronTrigger(minute='*/5', hour='8-20'),
        id='sync_attendance',
        name='Sync Attendance from Device',
        replace_existing=True
    )
    
    # Sync attendance every hour outside working hours
    scheduler.add_job(
        sync_attendance_job,
        CronTrigger(minute='0', hour='21-23,0-7'),
        id='sync_attendance_hourly',
        name='Sync Attendance Hourly',
        replace_existing=True
    )
    
    # Cleanup old logs daily at 2 AM
    scheduler.add_job(
        cleanup_old_logs_job,
        CronTrigger(hour=2, minute=0),
        id='cleanup_logs',
        name='Cleanup Old Logs',
        replace_existing=True
    )
    
    # Generate daily reports at 6 PM
    scheduler.add_job(
        generate_daily_reports_job,
        CronTrigger(hour=18, minute=0),
        id='daily_reports',
        name='Generate Daily Reports',
        replace_existing=True
    )
    
    # Start scheduler
    try:
        logger.info("Scheduler started. Press Ctrl+C to exit.")
        scheduler.start()
    except KeyboardInterrupt:
        logger.info("Scheduler stopped by user.")
    except Exception as e:
        logger.error(f"Scheduler error: {str(e)}")
    finally:
        scheduler.shutdown()

if __name__ == '__main__':
    main()
