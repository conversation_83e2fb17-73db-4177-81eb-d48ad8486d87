{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-user"></i> My Profile</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('auth.change_password') }}" class="btn btn-sm btn-outline-primary">
                <i class="fas fa-key"></i> Change Password
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- Profile Information -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-id-card"></i> Personal Information</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-xl bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-3">
                        <span class="h2 mb-0">{{ staff.first_name[0] }}{{ staff.last_name[0] }}</span>
                    </div>
                    <h4 class="mb-1">{{ staff.full_name }}</h4>
                    <p class="text-muted mb-0">{{ staff.staff_id }}</p>
                    <span class="badge bg-{{ 'success' if staff.is_active else 'secondary' }} mt-2">
                        {{ 'Active Employee' if staff.is_active else 'Inactive' }}
                    </span>
                </div>
                
                <hr>
                
                <div class="row g-3">
                    <div class="col-12">
                        <strong><i class="fas fa-envelope text-muted me-2"></i>Email:</strong><br>
                        <span class="text-muted">{{ staff.email }}</span>
                    </div>
                    {% if staff.phone %}
                    <div class="col-12">
                        <strong><i class="fas fa-phone text-muted me-2"></i>Phone:</strong><br>
                        <span class="text-muted">{{ staff.phone }}</span>
                    </div>
                    {% endif %}
                    {% if staff.date_of_birth %}
                    <div class="col-12">
                        <strong><i class="fas fa-birthday-cake text-muted me-2"></i>Date of Birth:</strong><br>
                        <span class="text-muted">{{ staff.date_of_birth.strftime('%B %d, %Y') }}</span>
                    </div>
                    {% endif %}
                    {% if staff.gender %}
                    <div class="col-12">
                        <strong><i class="fas fa-user text-muted me-2"></i>Gender:</strong><br>
                        <span class="text-muted">{{ staff.gender.title() }}</span>
                    </div>
                    {% endif %}
                    {% if staff.address %}
                    <div class="col-12">
                        <strong><i class="fas fa-map-marker-alt text-muted me-2"></i>Address:</strong><br>
                        <span class="text-muted">{{ staff.address }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Employment Information -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-briefcase"></i> Employment Details</h5>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    {% if staff.department %}
                    <div class="col-12">
                        <strong><i class="fas fa-building text-muted me-2"></i>Department:</strong><br>
                        <span class="badge bg-info">{{ staff.department }}</span>
                    </div>
                    {% endif %}
                    {% if staff.designation %}
                    <div class="col-12">
                        <strong><i class="fas fa-user-tie text-muted me-2"></i>Designation:</strong><br>
                        <span class="text-muted">{{ staff.designation }}</span>
                    </div>
                    {% endif %}
                    <div class="col-12">
                        <strong><i class="fas fa-calendar-plus text-muted me-2"></i>Date of Joining:</strong><br>
                        <span class="text-muted">{{ staff.date_of_joining.strftime('%B %d, %Y') }}</span>
                    </div>
                    <div class="col-12">
                        <strong><i class="fas fa-clock text-muted me-2"></i>Employee Type:</strong><br>
                        <span class="text-muted">{{ staff.employee_type.replace('_', ' ').title() }}</span>
                    </div>
                    <div class="col-12">
                        <strong><i class="fas fa-calendar text-muted me-2"></i>Years of Service:</strong><br>
                        {% set years = ((moment().date() - staff.date_of_joining).days / 365.25) | round(1) %}
                        <span class="text-muted">{{ years }} year{{ 's' if years != 1 else '' }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Editable Information -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-edit"></i> Update Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.phone.label(class="form-label") }}
                        {{ form.phone(class="form-control") }}
                        {% if form.phone.errors %}
                            <div class="text-danger">
                                {% for error in form.phone.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.address.label(class="form-label") }}
                        {{ form.address(class="form-control", rows="3") }}
                        {% if form.address.errors %}
                            <div class="text-danger">
                                {% for error in form.address.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid">
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <small class="text-muted">
                        <i class="fas fa-info-circle"></i>
                        To update other information, please contact HR department.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Quick Stats -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Quick Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h3 class="text-primary">{{ moment().date().day }}</h3>
                            <small class="text-muted">Days This Month</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h3 class="text-success">85%</h3>
                            <small class="text-muted">Attendance Rate</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="border-end">
                            <h3 class="text-info">160</h3>
                            <small class="text-muted">Hours This Month</small>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <h3 class="text-warning">5</h3>
                        <small class="text-muted">Leave Days Used</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

<style>
.avatar-xl {
    width: 100px;
    height: 100px;
    font-size: 36px;
}

.border-end {
    border-right: 1px solid #dee2e6;
}

@media (max-width: 768px) {
    .border-end {
        border-right: none;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 1rem;
        margin-bottom: 1rem;
    }
}
</style>
{% endblock %}
