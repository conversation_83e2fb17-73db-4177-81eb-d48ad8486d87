#!/usr/bin/env python3
"""
Database initialization script for Staff Management System
"""

import os
from datetime import date
from app import create_app, db
from app.models import User, Staff

def init_database():
    """Initialize the database with tables and default data"""
    app = create_app()
    
    with app.app_context():
        # Create all tables
        print("Creating database tables...")
        db.create_all()
        
        # Check if admin user already exists
        admin_user = User.query.filter_by(username='admin').first()
        if not admin_user:
            print("Creating default admin user...")
            
            # Create admin user
            admin_user = User(
                username='admin',
                email=app.config.get('ADMIN_EMAIL', '<EMAIL>'),
                role='admin'
            )
            admin_user.set_password(app.config.get('ADMIN_PASSWORD', 'admin123'))
            
            db.session.add(admin_user)
            db.session.commit()
            
            print(f"Admin user created:")
            print(f"  Username: admin")
            print(f"  Email: {admin_user.email}")
            print(f"  Password: {app.config.get('ADMIN_PASSWORD', 'admin123')}")
        else:
            print("Admin user already exists.")
        
        # Create sample staff if none exist
        if Staff.query.count() == 0:
            print("Creating sample staff members...")
            
            # Sample staff data
            sample_staff = [
                {
                    'staff_id': 'EMP001',
                    'first_name': 'John',
                    'last_name': 'Doe',
                    'email': '<EMAIL>',
                    'phone': '+1234567890',
                    'department': 'IT',
                    'designation': 'Software Developer',
                    'date_of_joining': date(2023, 1, 15),
                    'employee_type': 'full_time',
                    'salary': 50000.00
                },
                {
                    'staff_id': 'EMP002',
                    'first_name': 'Jane',
                    'last_name': 'Smith',
                    'email': '<EMAIL>',
                    'phone': '+1234567891',
                    'department': 'HR',
                    'designation': 'HR Manager',
                    'date_of_joining': date(2023, 2, 1),
                    'employee_type': 'full_time',
                    'salary': 60000.00
                },
                {
                    'staff_id': 'EMP003',
                    'first_name': 'Mike',
                    'last_name': 'Johnson',
                    'email': '<EMAIL>',
                    'phone': '+**********',
                    'department': 'Finance',
                    'designation': 'Accountant',
                    'date_of_joining': date(2023, 3, 1),
                    'employee_type': 'full_time',
                    'salary': 45000.00
                }
            ]
            
            for staff_data in sample_staff:
                staff = Staff(**staff_data)
                db.session.add(staff)
            
            db.session.commit()
            print(f"Created {len(sample_staff)} sample staff members.")
        
        print("\nDatabase initialization completed successfully!")
        print("\nNext steps:")
        print("1. Copy .env.example to .env and configure your settings")
        print("2. Install dependencies: pip install -r requirements.txt")
        print("3. Run the application: python app.py")
        print("4. Access the system at http://localhost:5000")

if __name__ == '__main__':
    init_database()
