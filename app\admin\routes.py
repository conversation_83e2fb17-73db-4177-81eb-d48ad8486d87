from flask import render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from sqlalchemy import func, and_
from app import db
from app.admin import bp
from app.admin.forms import StaffRegistrationForm, AttendanceEntryForm, LeaveApprovalForm, FingerprintRegistrationForm
from app.models import User, Staff, Attendance, LeaveRequest, FingerprintTemplate, AttendanceLog

def admin_required(f):
    """Decorator to require admin role"""
    def admin_decorated_function(*args, **kwargs):
        if not current_user.is_authenticated or not current_user.is_admin():
            flash('Access denied. Admin privileges required.', 'error')
            return redirect(url_for('main.index'))
        return f(*args, **kwargs)
    return admin_decorated_function

@bp.route('/dashboard')
@login_required
@admin_required
def dashboard():
    """Admin dashboard with overview statistics"""
    today = date.today()
    
    # Get statistics
    total_staff = Staff.query.filter_by(is_active=True).count()
    present_today = Attendance.query.filter(
        and_(Attendance.date == today, Attendance.status == 'present')
    ).count()
    absent_today = Attendance.query.filter(
        and_(Attendance.date == today, Attendance.status == 'absent')
    ).count()
    pending_leaves = LeaveRequest.query.filter_by(status='pending').count()
    
    # Recent attendance logs
    recent_logs = AttendanceLog.query.order_by(AttendanceLog.timestamp.desc()).limit(10).all()
    
    # Pending leave requests
    pending_leave_requests = LeaveRequest.query.filter_by(status='pending').order_by(
        LeaveRequest.applied_date.desc()
    ).limit(5).all()
    
    return render_template('admin/dashboard.html',
                         title='Admin Dashboard',
                         total_staff=total_staff,
                         present_today=present_today,
                         absent_today=absent_today,
                         pending_leaves=pending_leaves,
                         recent_logs=recent_logs,
                         pending_leave_requests=pending_leave_requests)

@bp.route('/staff')
@login_required
@admin_required
def staff_list():
    """List all staff members"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '', type=str)
    
    query = Staff.query
    if search:
        query = query.filter(
            (Staff.first_name.contains(search)) |
            (Staff.last_name.contains(search)) |
            (Staff.staff_id.contains(search)) |
            (Staff.email.contains(search))
        )
    
    staff_members = query.order_by(Staff.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False
    )
    
    return render_template('admin/staff_list.html',
                         title='Staff Management',
                         staff_members=staff_members,
                         search=search)

@bp.route('/staff/register', methods=['GET', 'POST'])
@login_required
@admin_required
def register_staff():
    """Register new staff member"""
    form = StaffRegistrationForm()
    
    if form.validate_on_submit():
        # Check if staff ID already exists
        existing_staff = Staff.query.filter_by(staff_id=form.staff_id.data).first()
        if existing_staff:
            flash('Staff ID already exists. Please use a different ID.', 'error')
            return render_template('admin/register_staff.html', form=form)
        
        # Check if email already exists
        existing_email = Staff.query.filter_by(email=form.email.data).first()
        if existing_email:
            flash('Email already exists. Please use a different email.', 'error')
            return render_template('admin/register_staff.html', form=form)
        
        staff = Staff(
            staff_id=form.staff_id.data,
            first_name=form.first_name.data,
            last_name=form.last_name.data,
            email=form.email.data,
            phone=form.phone.data,
            date_of_birth=form.date_of_birth.data,
            gender=form.gender.data,
            address=form.address.data,
            department=form.department.data,
            designation=form.designation.data,
            date_of_joining=form.date_of_joining.data,
            employee_type=form.employee_type.data,
            salary=form.salary.data,
            is_active=form.is_active.data
        )
        
        db.session.add(staff)
        db.session.commit()
        
        flash(f'Staff member {staff.full_name} registered successfully!', 'success')
        return redirect(url_for('admin.staff_list'))
    
    return render_template('admin/register_staff.html', title='Register Staff', form=form)

@bp.route('/staff/<int:staff_id>')
@login_required
@admin_required
def staff_detail(staff_id):
    """View staff member details"""
    staff = Staff.query.get_or_404(staff_id)

    # Get recent attendance
    recent_attendance = Attendance.query.filter_by(staff_id=staff_id).order_by(
        Attendance.date.desc()
    ).limit(10).all()

    # Get leave requests
    leave_requests = LeaveRequest.query.filter_by(staff_id=staff_id).order_by(
        LeaveRequest.applied_date.desc()
    ).limit(5).all()

    return render_template('admin/staff_detail.html',
                         title=f'Staff Details - {staff.full_name}',
                         staff=staff,
                         recent_attendance=recent_attendance,
                         leave_requests=leave_requests)

@bp.route('/attendance')
@login_required
@admin_required
def attendance_list():
    """View attendance records"""
    page = request.args.get('page', 1, type=int)
    date_filter = request.args.get('date', '', type=str)
    staff_filter = request.args.get('staff', '', type=str)

    query = Attendance.query.join(Staff)

    if date_filter:
        try:
            filter_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
            query = query.filter(Attendance.date == filter_date)
        except ValueError:
            flash('Invalid date format. Please use YYYY-MM-DD.', 'error')

    if staff_filter:
        query = query.filter(
            (Staff.first_name.contains(staff_filter)) |
            (Staff.last_name.contains(staff_filter)) |
            (Staff.staff_id.contains(staff_filter))
        )

    attendance_records = query.order_by(Attendance.date.desc(), Staff.staff_id).paginate(
        page=page, per_page=20, error_out=False
    )

    return render_template('admin/attendance_list.html',
                         title='Attendance Records',
                         attendance_records=attendance_records,
                         date_filter=date_filter,
                         staff_filter=staff_filter)

@bp.route('/attendance/manual-entry', methods=['GET', 'POST'])
@login_required
@admin_required
def manual_attendance_entry():
    """Manual attendance entry"""
    form = AttendanceEntryForm()

    # Populate staff choices
    staff_choices = [(s.id, f"{s.staff_id} - {s.full_name}")
                    for s in Staff.query.filter_by(is_active=True).order_by(Staff.staff_id).all()]
    form.staff_id.choices = staff_choices

    if form.validate_on_submit():
        # Check if attendance already exists for this date
        existing = Attendance.query.filter_by(
            staff_id=form.staff_id.data,
            date=form.date.data
        ).first()

        if existing:
            flash('Attendance record already exists for this date. Please edit the existing record.', 'error')
            return render_template('admin/manual_attendance_entry.html', form=form)

        # Parse time strings
        check_in_time = None
        check_out_time = None
        break_start_time = None
        break_end_time = None

        try:
            if form.check_in_time.data:
                check_in_time = datetime.combine(form.date.data,
                    datetime.strptime(form.check_in_time.data, '%H:%M').time())
            if form.check_out_time.data:
                check_out_time = datetime.combine(form.date.data,
                    datetime.strptime(form.check_out_time.data, '%H:%M').time())
            if form.break_start_time.data:
                break_start_time = datetime.combine(form.date.data,
                    datetime.strptime(form.break_start_time.data, '%H:%M').time())
            if form.break_end_time.data:
                break_end_time = datetime.combine(form.date.data,
                    datetime.strptime(form.break_end_time.data, '%H:%M').time())
        except ValueError:
            flash('Invalid time format. Please use HH:MM format.', 'error')
            return render_template('admin/manual_attendance_entry.html', form=form)

        attendance = Attendance(
            staff_id=form.staff_id.data,
            date=form.date.data,
            check_in_time=check_in_time,
            check_out_time=check_out_time,
            break_start_time=break_start_time,
            break_end_time=break_end_time,
            status=form.status.data,
            notes=form.notes.data,
            is_manual_entry=True
        )

        # Calculate total hours
        attendance.calculate_total_hours()

        db.session.add(attendance)
        db.session.commit()

        flash('Attendance record added successfully!', 'success')
        return redirect(url_for('admin.attendance_list'))

    return render_template('admin/manual_attendance_entry.html',
                         title='Manual Attendance Entry', form=form)
