from flask_wtf import <PERSON>laskForm
from wtforms import <PERSON>Field, TextAreaField, SelectField, DateField, SubmitField, IntegerField
from wtforms.validators import DataRequired, Length, Optional
from wtforms.widgets import TextArea
from datetime import date

class LeaveRequestForm(FlaskForm):
    leave_type = SelectField('Leave Type', 
                            choices=[('sick', 'Sick Leave'), ('casual', 'Casual Leave'), 
                                   ('annual', 'Annual Leave'), ('maternity', 'Maternity Leave'),
                                   ('paternity', 'Paternity Leave'), ('emergency', 'Emergency Leave')],
                            validators=[DataRequired()])
    start_date = DateField('Start Date', validators=[DataRequired()])
    end_date = DateField('End Date', validators=[DataRequired()])
    reason = TextAreaField('Reason', validators=[DataRequired(), Length(min=10, max=500)], widget=TextArea())
    submit = SubmitField('Submit Leave Request')
    
    def validate(self):
        if not super().validate():
            return False
        
        if self.start_date.data > self.end_date.data:
            self.end_date.errors.append('End date must be after start date.')
            return False
        
        if self.start_date.data < date.today():
            self.start_date.errors.append('Start date cannot be in the past.')
            return False
        
        return True

class ProfileUpdateForm(FlaskForm):
    phone = StringField('Phone', validators=[Length(max=15)])
    address = TextAreaField('Address', widget=TextArea())
    submit = SubmitField('Update Profile')
