"""
Automatic Biometric Device Detection and Configuration Service
Automatically detects ESSL devices on the local network and configures the system
"""

import socket
import struct
import threading
import time
import os
from datetime import datetime
from flask import current_app

class BiometricAutoDetector:
    """Automatically detects and configures biometric devices"""
    
    def __init__(self):
        self.detected_devices = []
        self.active_devices = []
        self.detection_complete = False
        self.primary_device = None
        
    def quick_scan(self, timeout=3):
        """Quick scan for devices on common IPs"""
        print("🔍 Quick scanning for biometric devices...")
        
        common_ips = [
            "*************", "*************", "*************", "*************",
            "*************", "*************", "*************", "*************",
            "**********", "**********", "**********",
            "************", "************"
        ]
        
        found_devices = []
        
        # Test common IPs quickly
        for ip in common_ips:
            device_info = self._test_device_quick(ip, timeout=timeout)
            if device_info:
                found_devices.append(device_info)
                print(f"✅ Found device: {ip}")
        
        return found_devices
    
    def full_network_scan(self, max_threads=30):
        """Full network scan for devices"""
        print("🌐 Performing full network scan...")
        
        networks = self._get_local_networks()
        found_devices = []
        
        for network in networks[:2]:  # Limit to 2 networks for speed
            devices = self._scan_network_threaded(network, max_threads)
            found_devices.extend(devices)
        
        return found_devices
    
    def auto_detect_and_configure(self):
        """Main auto-detection and configuration method"""
        print("🚀 Starting automatic device detection...")

        # Step 1: Scan for local USB/Serial devices
        local_devices = self.scan_local_devices()

        # Step 2: Quick network scan
        network_devices = self.quick_scan()

        # Step 3: If no devices found, do full network scan
        if not network_devices and not local_devices:
            print("📡 No devices found in quick scans, performing full network scan...")
            network_devices = self.full_network_scan()

        # Combine all found devices
        all_devices = local_devices + network_devices

        # Step 4: Configure system if devices found
        if all_devices:
            self.detected_devices = all_devices
            # Prefer local devices as primary, then network devices
            self.primary_device = local_devices[0] if local_devices else network_devices[0]

            print(f"🎉 Found {len(all_devices)} device(s)!")
            if local_devices:
                print(f"   📱 Local devices: {len(local_devices)}")
                for i, device in enumerate(local_devices, 1):
                    print(f"      Local {i}: {device['connection']} - {device['model']}")
            if network_devices:
                print(f"   🌐 Network devices: {len(network_devices)}")
                for i, device in enumerate(network_devices, 1):
                    print(f"      Network {i}: {device['ip']}:{device['port']} - {device['model']}")

            # Auto-configure the system
            self._auto_configure_system(all_devices)
            return True
        else:
            print("❌ No biometric devices found locally or on network")
            return False
    
    def _test_device_quick(self, ip, port=4370, timeout=3):
        """Quick test of a single device"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(timeout)
            
            # Create connection command
            command = self._create_command(1000, b'')  # CMD_CONNECT
            sock.sendto(command, (ip, port))
            
            # Wait for response
            data, addr = sock.recvfrom(1024)
            sock.close()
            
            if len(data) >= 8:
                reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
                if command_id == 2000:  # CMD_ACK_OK
                    return {
                        'ip': ip,
                        'port': port,
                        'model': 'ESSL X990+ID',
                        'session_id': session_id,
                        'status': 'connected',
                        'detected_at': datetime.now().isoformat()
                    }
            return None
            
        except (socket.timeout, socket.error, Exception):
            return None
    
    def _scan_network_threaded(self, network_base, max_threads=30):
        """Scan network range using threading"""
        import queue
        
        print(f"🔍 Scanning network {network_base}.1-254...")
        device_queue = queue.Queue()
        
        def scan_ip(ip):
            device_info = self._test_device_quick(ip, timeout=2)
            if device_info:
                device_queue.put(device_info)
        
        # Create and start threads
        threads = []
        for i in range(1, 255):
            ip = f"{network_base}.{i}"
            thread = threading.Thread(target=scan_ip, args=(ip,))
            threads.append(thread)
            thread.start()
            
            # Limit concurrent threads
            if len(threads) >= max_threads:
                for t in threads:
                    t.join()
                threads = []
        
        # Wait for remaining threads
        for thread in threads:
            thread.join()
        
        # Collect results
        found_devices = []
        while not device_queue.empty():
            device_info = device_queue.get()
            found_devices.append(device_info)
            print(f"✅ Found device: {device_info['ip']}")
        
        return found_devices
    
    def _get_local_networks(self):
        """Get local network ranges"""
        networks = []
        
        try:
            import netifaces
            for interface in netifaces.interfaces():
                addrs = netifaces.ifaddresses(interface)
                if netifaces.AF_INET in addrs:
                    for addr_info in addrs[netifaces.AF_INET]:
                        ip = addr_info.get('addr')
                        if ip and not ip.startswith('127.'):
                            network_base = '.'.join(ip.split('.')[:-1])
                            if network_base not in networks:
                                networks.append(network_base)
        except ImportError:
            # Fallback networks
            networks = ["192.168.1", "192.168.0", "10.0.0", "172.16.0"]
        
        return networks or ["192.168.1", "192.168.0"]
    
    def _auto_configure_system(self, devices):
        """Automatically configure the system with detected devices"""
        print("⚙️ Auto-configuring system...")
        
        # Update environment configuration
        self._update_env_config(devices)
        
        # Update Flask app config if running
        try:
            if current_app:
                self._update_app_config(devices)
        except RuntimeError:
            pass  # No app context
        
        print("✅ System auto-configured successfully!")
    
    def _update_env_config(self, devices):
        """Update .env file with detected devices"""
        primary = devices[0]
        device_ips = [d['ip'] for d in devices]
        
        env_updates = {
            'ESSL_DEVICE_IP': primary['ip'],
            'ESSL_DEVICE_PORT': str(primary['port']),
            'ESSL_DEVICE_LIST': ','.join(device_ips),
            'ESSL_DEVICE_COUNT': str(len(devices)),
            'AUTO_DETECT_DEVICES': 'true'
        }
        
        # Read existing .env or create new one
        env_file = '.env'
        env_content = {}
        
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.strip().startswith('#'):
                        key, value = line.strip().split('=', 1)
                        env_content[key] = value
        
        # Update with new device config
        env_content.update(env_updates)
        
        # Write back to file
        with open(env_file, 'w') as f:
            f.write("# Auto-generated device configuration\n")
            f.write(f"# Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Detected {len(devices)} device(s)\n\n")
            
            for key, value in env_content.items():
                f.write(f"{key}={value}\n")
            
            f.write(f"\n# Detected devices:\n")
            for i, device in enumerate(devices, 1):
                f.write(f"# Device {i}: {device['ip']}:{device['port']} - {device['model']}\n")
        
        print(f"📝 Updated .env with {len(devices)} device(s)")
    
    def _update_app_config(self, devices):
        """Update Flask app configuration"""
        primary = devices[0]
        device_ips = [d['ip'] for d in devices]
        
        current_app.config['ESSL_DEVICE_IP'] = primary['ip']
        current_app.config['ESSL_DEVICE_PORT'] = primary['port']
        current_app.config['ESSL_DEVICE_LIST'] = ','.join(device_ips)
        current_app.config['ESSL_DEVICE_COUNT'] = len(devices)
        current_app.config['AUTO_DETECT_DEVICES'] = True
        
        print("🔧 Updated Flask app configuration")
    
    def _create_command(self, command_id, data, session_id=0):
        """Create command packet for device communication"""
        reply_id = 1
        checksum = sum(data) & 0xFFFF if data else 0
        header = struct.pack('<HHHH', reply_id, command_id, checksum, session_id)
        return header + data
    
    def get_detected_devices(self):
        """Get list of detected devices"""
        return self.detected_devices
    
    def get_primary_device(self):
        """Get primary device info"""
        return self.primary_device

    def scan_local_devices(self):
        """Scan for locally connected biometric devices (USB, Serial, etc.)"""
        print("🔌 Scanning for local biometric devices...")

        local_devices = []

        # Scan USB devices
        usb_devices = self._scan_usb_devices()
        local_devices.extend(usb_devices)

        # Scan Serial/COM ports
        serial_devices = self._scan_serial_devices()
        local_devices.extend(serial_devices)

        # Scan for local network interfaces (localhost, 127.0.0.1)
        localhost_devices = self._scan_localhost_devices()
        local_devices.extend(localhost_devices)

        if local_devices:
            print(f"✅ Found {len(local_devices)} local device(s)")
        else:
            print("ℹ️ No local devices found")

        return local_devices

    def _scan_usb_devices(self):
        """Scan for USB-connected biometric devices"""
        usb_devices = []

        try:
            import usb.core
            import usb.util

            print("   🔍 Scanning USB devices...")

            # Common vendor IDs for biometric devices
            biometric_vendors = [
                0x2808,  # ESSL
                0x1234,  # Generic biometric
                0x0483,  # STMicroelectronics (used in some biometric devices)
                0x04e8,  # Samsung (some biometric devices)
                0x27c6,  # Goodix (fingerprint sensors)
            ]

            devices = usb.core.find(find_all=True)

            for device in devices:
                try:
                    if device.idVendor in biometric_vendors:
                        device_info = {
                            'connection': f'USB (VID:{device.idVendor:04x}, PID:{device.idProduct:04x})',
                            'model': 'USB Biometric Device',
                            'type': 'usb',
                            'vendor_id': device.idVendor,
                            'product_id': device.idProduct,
                            'status': 'connected',
                            'detected_at': datetime.now().isoformat()
                        }

                        # Try to get device description
                        try:
                            device_info['description'] = usb.util.get_string(device, device.iProduct)
                        except:
                            device_info['description'] = 'Unknown USB Biometric Device'

                        usb_devices.append(device_info)
                        print(f"      ✅ Found USB device: VID:{device.idVendor:04x} PID:{device.idProduct:04x}")

                except Exception as e:
                    continue  # Skip devices we can't access

        except ImportError:
            print("      ⚠️ pyusb not installed - skipping USB scan")
            print("      Install with: pip install pyusb")
        except Exception as e:
            print(f"      ⚠️ USB scan error: {str(e)}")

        return usb_devices

    def _scan_serial_devices(self):
        """Scan for Serial/COM port connected devices"""
        serial_devices = []

        try:
            import serial.tools.list_ports

            print("   🔍 Scanning Serial/COM ports...")

            ports = serial.tools.list_ports.comports()

            for port in ports:
                try:
                    # Check if it might be a biometric device
                    if self._is_biometric_serial_device(port):
                        device_info = {
                            'connection': f'Serial ({port.device})',
                            'model': 'Serial Biometric Device',
                            'type': 'serial',
                            'port': port.device,
                            'description': port.description,
                            'hwid': port.hwid,
                            'status': 'connected',
                            'detected_at': datetime.now().isoformat()
                        }

                        serial_devices.append(device_info)
                        print(f"      ✅ Found Serial device: {port.device} - {port.description}")

                except Exception as e:
                    continue

        except ImportError:
            print("      ⚠️ pyserial not installed - skipping Serial scan")
            print("      Install with: pip install pyserial")
        except Exception as e:
            print(f"      ⚠️ Serial scan error: {str(e)}")

        return serial_devices

    def _scan_localhost_devices(self):
        """Scan localhost for biometric devices (devices with local network interface)"""
        localhost_devices = []

        print("   🔍 Scanning localhost interfaces...")

        # Test common localhost addresses and ports
        localhost_addresses = ['127.0.0.1', 'localhost']
        common_ports = [4370, 8080, 9999, 8000, 5000]

        for address in localhost_addresses:
            for port in common_ports:
                device_info = self._test_device_quick(address, port, timeout=1)
                if device_info:
                    device_info['type'] = 'localhost'
                    device_info['connection'] = f'Localhost ({address}:{port})'
                    localhost_devices.append(device_info)
                    print(f"      ✅ Found localhost device: {address}:{port}")

        return localhost_devices

    def _is_biometric_serial_device(self, port):
        """Check if a serial port might be a biometric device"""
        # Check device description for biometric keywords
        description = (port.description or '').lower()
        hwid = (port.hwid or '').lower()

        biometric_keywords = [
            'biometric', 'fingerprint', 'essl', 'attendance',
            'scanner', 'reader', 'sensor', 'zktech', 'zk'
        ]

        # Check if description contains biometric keywords
        for keyword in biometric_keywords:
            if keyword in description or keyword in hwid:
                return True

        # Check for specific vendor IDs in HWID
        biometric_vid_patterns = ['VID_2808', 'VID_1234', 'VID_0483']
        for pattern in biometric_vid_patterns:
            if pattern in hwid:
                return True

        return False

# Global auto-detector instance
auto_detector = BiometricAutoDetector()

def run_auto_detection():
    """Run automatic device detection"""
    return auto_detector.auto_detect_and_configure()

def get_auto_detector():
    """Get the global auto-detector instance"""
    return auto_detector
