"""
Automatic Biometric Device Detection and Configuration Service
Automatically detects ESSL devices on the local network and configures the system
"""

import socket
import struct
import threading
import time
import os
from datetime import datetime
from flask import current_app

class BiometricAutoDetector:
    """Automatically detects and configures biometric devices"""
    
    def __init__(self):
        self.detected_devices = []
        self.active_devices = []
        self.detection_complete = False
        self.primary_device = None
        
    def quick_scan(self, timeout=3):
        """Quick scan for devices on common IPs"""
        print("🔍 Quick scanning for biometric devices...")
        
        common_ips = [
            "*************", "*************", "*************", "*************",
            "*************", "*************", "*************", "*************",
            "**********", "**********", "**********",
            "************", "************"
        ]
        
        found_devices = []
        
        # Test common IPs quickly
        for ip in common_ips:
            device_info = self._test_device_quick(ip, timeout=timeout)
            if device_info:
                found_devices.append(device_info)
                print(f"✅ Found device: {ip}")
        
        return found_devices
    
    def full_network_scan(self, max_threads=30):
        """Full network scan for devices"""
        print("🌐 Performing full network scan...")
        
        networks = self._get_local_networks()
        found_devices = []
        
        for network in networks[:2]:  # Limit to 2 networks for speed
            devices = self._scan_network_threaded(network, max_threads)
            found_devices.extend(devices)
        
        return found_devices
    
    def auto_detect_and_configure(self):
        """Main auto-detection and configuration method"""
        print("🚀 Starting automatic device detection...")
        
        # Step 1: Quick scan
        devices = self.quick_scan()
        
        # Step 2: If no devices found, do full scan
        if not devices:
            print("📡 No devices found in quick scan, performing full network scan...")
            devices = self.full_network_scan()
        
        # Step 3: Configure system if devices found
        if devices:
            self.detected_devices = devices
            self.primary_device = devices[0]  # Use first device as primary
            
            print(f"🎉 Found {len(devices)} device(s)!")
            for i, device in enumerate(devices, 1):
                print(f"   Device {i}: {device['ip']}:{device['port']} - {device['model']}")
            
            # Auto-configure the system
            self._auto_configure_system(devices)
            return True
        else:
            print("❌ No biometric devices found on the network")
            return False
    
    def _test_device_quick(self, ip, port=4370, timeout=3):
        """Quick test of a single device"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(timeout)
            
            # Create connection command
            command = self._create_command(1000, b'')  # CMD_CONNECT
            sock.sendto(command, (ip, port))
            
            # Wait for response
            data, addr = sock.recvfrom(1024)
            sock.close()
            
            if len(data) >= 8:
                reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
                if command_id == 2000:  # CMD_ACK_OK
                    return {
                        'ip': ip,
                        'port': port,
                        'model': 'ESSL X990+ID',
                        'session_id': session_id,
                        'status': 'connected',
                        'detected_at': datetime.now().isoformat()
                    }
            return None
            
        except (socket.timeout, socket.error, Exception):
            return None
    
    def _scan_network_threaded(self, network_base, max_threads=30):
        """Scan network range using threading"""
        import queue
        
        print(f"🔍 Scanning network {network_base}.1-254...")
        device_queue = queue.Queue()
        
        def scan_ip(ip):
            device_info = self._test_device_quick(ip, timeout=2)
            if device_info:
                device_queue.put(device_info)
        
        # Create and start threads
        threads = []
        for i in range(1, 255):
            ip = f"{network_base}.{i}"
            thread = threading.Thread(target=scan_ip, args=(ip,))
            threads.append(thread)
            thread.start()
            
            # Limit concurrent threads
            if len(threads) >= max_threads:
                for t in threads:
                    t.join()
                threads = []
        
        # Wait for remaining threads
        for thread in threads:
            thread.join()
        
        # Collect results
        found_devices = []
        while not device_queue.empty():
            device_info = device_queue.get()
            found_devices.append(device_info)
            print(f"✅ Found device: {device_info['ip']}")
        
        return found_devices
    
    def _get_local_networks(self):
        """Get local network ranges"""
        networks = []
        
        try:
            import netifaces
            for interface in netifaces.interfaces():
                addrs = netifaces.ifaddresses(interface)
                if netifaces.AF_INET in addrs:
                    for addr_info in addrs[netifaces.AF_INET]:
                        ip = addr_info.get('addr')
                        if ip and not ip.startswith('127.'):
                            network_base = '.'.join(ip.split('.')[:-1])
                            if network_base not in networks:
                                networks.append(network_base)
        except ImportError:
            # Fallback networks
            networks = ["192.168.1", "192.168.0", "10.0.0", "172.16.0"]
        
        return networks or ["192.168.1", "192.168.0"]
    
    def _auto_configure_system(self, devices):
        """Automatically configure the system with detected devices"""
        print("⚙️ Auto-configuring system...")
        
        # Update environment configuration
        self._update_env_config(devices)
        
        # Update Flask app config if running
        try:
            if current_app:
                self._update_app_config(devices)
        except RuntimeError:
            pass  # No app context
        
        print("✅ System auto-configured successfully!")
    
    def _update_env_config(self, devices):
        """Update .env file with detected devices"""
        primary = devices[0]
        device_ips = [d['ip'] for d in devices]
        
        env_updates = {
            'ESSL_DEVICE_IP': primary['ip'],
            'ESSL_DEVICE_PORT': str(primary['port']),
            'ESSL_DEVICE_LIST': ','.join(device_ips),
            'ESSL_DEVICE_COUNT': str(len(devices)),
            'AUTO_DETECT_DEVICES': 'true'
        }
        
        # Read existing .env or create new one
        env_file = '.env'
        env_content = {}
        
        if os.path.exists(env_file):
            with open(env_file, 'r') as f:
                for line in f:
                    if '=' in line and not line.strip().startswith('#'):
                        key, value = line.strip().split('=', 1)
                        env_content[key] = value
        
        # Update with new device config
        env_content.update(env_updates)
        
        # Write back to file
        with open(env_file, 'w') as f:
            f.write("# Auto-generated device configuration\n")
            f.write(f"# Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# Detected {len(devices)} device(s)\n\n")
            
            for key, value in env_content.items():
                f.write(f"{key}={value}\n")
            
            f.write(f"\n# Detected devices:\n")
            for i, device in enumerate(devices, 1):
                f.write(f"# Device {i}: {device['ip']}:{device['port']} - {device['model']}\n")
        
        print(f"📝 Updated .env with {len(devices)} device(s)")
    
    def _update_app_config(self, devices):
        """Update Flask app configuration"""
        primary = devices[0]
        device_ips = [d['ip'] for d in devices]
        
        current_app.config['ESSL_DEVICE_IP'] = primary['ip']
        current_app.config['ESSL_DEVICE_PORT'] = primary['port']
        current_app.config['ESSL_DEVICE_LIST'] = ','.join(device_ips)
        current_app.config['ESSL_DEVICE_COUNT'] = len(devices)
        current_app.config['AUTO_DETECT_DEVICES'] = True
        
        print("🔧 Updated Flask app configuration")
    
    def _create_command(self, command_id, data, session_id=0):
        """Create command packet for device communication"""
        reply_id = 1
        checksum = sum(data) & 0xFFFF if data else 0
        header = struct.pack('<HHHH', reply_id, command_id, checksum, session_id)
        return header + data
    
    def get_detected_devices(self):
        """Get list of detected devices"""
        return self.detected_devices
    
    def get_primary_device(self):
        """Get primary device info"""
        return self.primary_device

# Global auto-detector instance
auto_detector = BiometricAutoDetector()

def run_auto_detection():
    """Run automatic device detection"""
    return auto_detector.auto_detect_and_configure()

def get_auto_detector():
    """Get the global auto-detector instance"""
    return auto_detector
