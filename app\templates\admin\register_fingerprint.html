{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-fingerprint"></i> Register Fingerprint</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.staff_detail', staff_id=staff.id) }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Staff Details
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user"></i> {{ staff.full_name }} ({{ staff.staff_id }})
                </h5>
            </div>
            <div class="card-body">
                <!-- Device Connection Status -->
                <div class="alert alert-info" id="device-status">
                    <i class="fas fa-info-circle"></i>
                    <strong>Device Status:</strong> <span id="status-text">Checking connection...</span>
                </div>
                
                <form method="POST" id="fingerprint-form">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.staff_id.label(class="form-label") }}
                            {{ form.staff_id(class="form-select", readonly=true, disabled=true) }}
                            <input type="hidden" name="staff_id" value="{{ staff.id }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.device_user_id.label(class="form-label") }}
                            {{ form.device_user_id(class="form-control") }}
                            {% if form.device_user_id.errors %}
                                <div class="text-danger">
                                    {% for error in form.device_user_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Unique ID for biometric device (1-9999)</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.finger_id.label(class="form-label") }}
                            {{ form.finger_id(class="form-select") }}
                            {% if form.finger_id.errors %}
                                <div class="text-danger">
                                    {% for error in form.finger_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <!-- Fingerprint Capture Section -->
                    <div class="card bg-light mb-3">
                        <div class="card-header">
                            <h6 class="mb-0"><i class="fas fa-hand-paper"></i> Fingerprint Capture</h6>
                        </div>
                        <div class="card-body text-center">
                            <div id="capture-status" class="mb-3">
                                <i class="fas fa-fingerprint fa-4x text-muted mb-3"></i>
                                <h5>Ready to Capture</h5>
                                <p class="text-muted">Click "Start Capture" and place finger on the biometric device</p>
                            </div>
                            
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-primary" id="start-capture" onclick="startFingerprintCapture()">
                                    <i class="fas fa-play"></i> Start Capture
                                </button>
                                <button type="button" class="btn btn-secondary" id="test-device" onclick="testDevice()">
                                    <i class="fas fa-wifi"></i> Test Device
                                </button>
                            </div>
                            
                            <!-- Progress indicator -->
                            <div class="progress mt-3" id="capture-progress" style="display: none;">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 0%"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.staff_list') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Skip for Now
                        </a>
                        {{ form.submit(class="btn btn-success", id="register-btn", disabled=true) }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Instructions</h5>
            </div>
            <div class="card-body">
                <h6>Fingerprint Registration Steps:</h6>
                <ol class="small">
                    <li>Ensure ESSL X990+ID device is connected</li>
                    <li>Select the finger to register</li>
                    <li>Click "Start Capture"</li>
                    <li>Place finger firmly on the scanner</li>
                    <li>Hold steady until capture completes</li>
                    <li>Click "Register Fingerprint" to save</li>
                </ol>
                
                <hr>
                
                <h6>Device Requirements:</h6>
                <ul class="small">
                    <li>ESSL X990+ID biometric device</li>
                    <li>Network connection established</li>
                    <li>Device IP configured in system</li>
                    <li>Clean finger surface for best results</li>
                </ul>
                
                <hr>
                
                <div class="alert alert-warning small">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Note:</strong> Each finger can only be registered once per staff member.
                </div>
            </div>
        </div>
        
        <!-- Finger Selection Guide -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-hand-paper"></i> Finger Guide</h5>
            </div>
            <div class="card-body">
                <div class="text-center">
                    <svg width="200" height="120" viewBox="0 0 200 120">
                        <!-- Left Hand -->
                        <g transform="translate(20,20)">
                            <text x="40" y="10" text-anchor="middle" class="small">Left Hand</text>
                            <rect x="10" y="15" width="8" height="25" fill="#e9ecef" stroke="#6c757d" rx="4"/>
                            <rect x="20" y="12" width="8" height="30" fill="#e9ecef" stroke="#6c757d" rx="4"/>
                            <rect x="30" y="10" width="8" height="32" fill="#e9ecef" stroke="#6c757d" rx="4"/>
                            <rect x="40" y="12" width="8" height="30" fill="#e9ecef" stroke="#6c757d" rx="4"/>
                            <rect x="50" y="20" width="8" height="20" fill="#e9ecef" stroke="#6c757d" rx="4"/>
                            
                            <text x="14" y="55" text-anchor="middle" class="tiny">L5</text>
                            <text x="24" y="55" text-anchor="middle" class="tiny">L4</text>
                            <text x="34" y="55" text-anchor="middle" class="tiny">L3</text>
                            <text x="44" y="55" text-anchor="middle" class="tiny">L2</text>
                            <text x="54" y="55" text-anchor="middle" class="tiny">L1</text>
                        </g>
                        
                        <!-- Right Hand -->
                        <g transform="translate(120,20)">
                            <text x="40" y="10" text-anchor="middle" class="small">Right Hand</text>
                            <rect x="10" y="20" width="8" height="20" fill="#e9ecef" stroke="#6c757d" rx="4"/>
                            <rect x="20" y="12" width="8" height="30" fill="#e9ecef" stroke="#6c757d" rx="4"/>
                            <rect x="30" y="10" width="8" height="32" fill="#e9ecef" stroke="#6c757d" rx="4"/>
                            <rect x="40" y="12" width="8" height="30" fill="#e9ecef" stroke="#6c757d" rx="4"/>
                            <rect x="50" y="15" width="8" height="25" fill="#e9ecef" stroke="#6c757d" rx="4"/>
                            
                            <text x="14" y="55" text-anchor="middle" class="tiny">R1</text>
                            <text x="24" y="55" text-anchor="middle" class="tiny">R2</text>
                            <text x="34" y="55" text-anchor="middle" class="tiny">R3</text>
                            <text x="44" y="55" text-anchor="middle" class="tiny">R4</text>
                            <text x="54" y="55" text-anchor="middle" class="tiny">R5</text>
                        </g>
                    </svg>
                    
                    <small class="text-muted d-block mt-2">
                        Recommended: Use index fingers (R2/L2) for primary registration
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let captureInProgress = false;
let fingerprintCaptured = false;

// Check device status on page load
document.addEventListener('DOMContentLoaded', function() {
    checkDeviceStatus();
});

function checkDeviceStatus() {
    fetch('/api/device/status')
        .then(response => response.json())
        .then(data => {
            const statusElement = document.getElementById('status-text');
            const alertElement = document.getElementById('device-status');
            
            if (data.connected) {
                statusElement.textContent = 'Connected to ESSL X990+ID';
                alertElement.className = 'alert alert-success';
            } else {
                statusElement.textContent = 'Device not connected';
                alertElement.className = 'alert alert-danger';
            }
        })
        .catch(error => {
            document.getElementById('status-text').textContent = 'Connection check failed';
            document.getElementById('device-status').className = 'alert alert-warning';
        });
}

function testDevice() {
    const btn = document.getElementById('test-device');
    const originalText = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    btn.disabled = true;
    
    fetch('/api/device/test', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert('Device test successful!', 'success');
                checkDeviceStatus();
            } else {
                showAlert('Device test failed: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('Device test error: ' + error.message, 'danger');
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
}

function startFingerprintCapture() {
    if (captureInProgress) return;
    
    const staffId = {{ staff.id }};
    const fingerId = document.getElementById('finger_id').value;
    
    if (!fingerId) {
        showAlert('Please select a finger first', 'warning');
        return;
    }
    
    captureInProgress = true;
    const btn = document.getElementById('start-capture');
    const statusDiv = document.getElementById('capture-status');
    const progressDiv = document.getElementById('capture-progress');
    
    // Update UI
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Capturing...';
    btn.disabled = true;
    progressDiv.style.display = 'block';
    
    statusDiv.innerHTML = `
        <i class="fas fa-fingerprint fa-4x text-primary mb-3"></i>
        <h5 class="text-primary">Capturing Fingerprint...</h5>
        <p class="text-muted">Please place your finger on the biometric device and hold steady</p>
    `;
    
    // Simulate progress
    let progress = 0;
    const progressBar = progressDiv.querySelector('.progress-bar');
    const progressInterval = setInterval(() => {
        progress += 10;
        progressBar.style.width = progress + '%';
        
        if (progress >= 100) {
            clearInterval(progressInterval);
        }
    }, 200);
    
    // Make API call to capture fingerprint
    fetch(`/admin/fingerprint/capture/${staffId}/${fingerId}`)
        .then(response => response.json())
        .then(data => {
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            
            if (data.success) {
                fingerprintCaptured = true;
                statusDiv.innerHTML = `
                    <i class="fas fa-check-circle fa-4x text-success mb-3"></i>
                    <h5 class="text-success">Fingerprint Captured Successfully!</h5>
                    <p class="text-muted">You can now register this fingerprint</p>
                `;
                
                document.getElementById('register-btn').disabled = false;
                showAlert('Fingerprint captured successfully!', 'success');
            } else {
                statusDiv.innerHTML = `
                    <i class="fas fa-times-circle fa-4x text-danger mb-3"></i>
                    <h5 class="text-danger">Capture Failed</h5>
                    <p class="text-muted">${data.message}</p>
                `;
                showAlert('Fingerprint capture failed: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            clearInterval(progressInterval);
            statusDiv.innerHTML = `
                <i class="fas fa-exclamation-triangle fa-4x text-warning mb-3"></i>
                <h5 class="text-warning">Capture Error</h5>
                <p class="text-muted">Please try again</p>
            `;
            showAlert('Capture error: ' + error.message, 'danger');
        })
        .finally(() => {
            captureInProgress = false;
            btn.innerHTML = '<i class="fas fa-redo"></i> Retry Capture';
            btn.disabled = false;
            
            setTimeout(() => {
                progressDiv.style.display = 'none';
                progressBar.style.width = '0%';
            }, 2000);
        });
}

function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    const container = document.querySelector('.container-fluid');
    container.insertBefore(alertDiv, container.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Form validation
document.getElementById('fingerprint-form').addEventListener('submit', function(e) {
    if (!fingerprintCaptured) {
        e.preventDefault();
        showAlert('Please capture a fingerprint before registering', 'warning');
    }
});
</script>

<style>
.tiny {
    font-size: 8px;
}
</style>
{% endblock %}
