from flask import render_template, redirect, url_for, flash, request
from flask_login import login_required, current_user
from datetime import datetime, date, timedelta
from sqlalchemy import and_
from functools import wraps
from app import db
from app.staff import bp
from app.staff.forms import LeaveRequestForm, ProfileUpdateForm
from app.models import Staff, Attendance, LeaveRequest

def staff_required(f):
    """Decorator to ensure user has staff access"""
    @wraps(f)
    def staff_decorated_function(*args, **kwargs):
        if not current_user.is_authenticated:
            return redirect(url_for('auth.login'))
        return f(*args, **kwargs)
    return staff_decorated_function

@bp.route('/dashboard')
@login_required
@staff_required
def dashboard():
    """Staff dashboard"""
    # Get staff record
    staff = Staff.query.filter_by(user_id=current_user.id).first()
    if not staff:
        flash('Staff profile not found. Please contact administrator.', 'error')
        return redirect(url_for('main.index'))
    
    today = date.today()
    current_month = today.replace(day=1)
    
    # Get current month attendance
    monthly_attendance = Attendance.query.filter(
        and_(
            Attendance.staff_id == staff.id,
            Attendance.date >= current_month,
            Attendance.date <= today
        )
    ).order_by(Attendance.date.desc()).all()
    
    # Calculate statistics
    total_days = len(monthly_attendance)
    present_days = len([a for a in monthly_attendance if a.status == 'present'])
    absent_days = len([a for a in monthly_attendance if a.status == 'absent'])
    late_days = len([a for a in monthly_attendance if a.status == 'late'])
    
    # Get recent leave requests
    recent_leaves = LeaveRequest.query.filter_by(staff_id=staff.id).order_by(
        LeaveRequest.applied_date.desc()
    ).limit(5).all()
    
    # Today's attendance
    today_attendance = Attendance.query.filter(
        and_(Attendance.staff_id == staff.id, Attendance.date == today)
    ).first()
    
    return render_template('staff/dashboard.html',
                         title='Staff Dashboard',
                         staff=staff,
                         monthly_attendance=monthly_attendance,
                         total_days=total_days,
                         present_days=present_days,
                         absent_days=absent_days,
                         late_days=late_days,
                         recent_leaves=recent_leaves,
                         today_attendance=today_attendance)

@bp.route('/attendance')
@login_required
@staff_required
def attendance_history():
    """View attendance history"""
    staff = Staff.query.filter_by(user_id=current_user.id).first()
    if not staff:
        flash('Staff profile not found.', 'error')
        return redirect(url_for('main.index'))
    
    page = request.args.get('page', 1, type=int)
    month = request.args.get('month', '', type=str)
    
    query = Attendance.query.filter_by(staff_id=staff.id)
    
    if month:
        try:
            month_date = datetime.strptime(month, '%Y-%m').date()
            start_date = month_date.replace(day=1)
            if month_date.month == 12:
                end_date = month_date.replace(year=month_date.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end_date = month_date.replace(month=month_date.month + 1, day=1) - timedelta(days=1)
            
            query = query.filter(and_(Attendance.date >= start_date, Attendance.date <= end_date))
        except ValueError:
            flash('Invalid month format. Please use YYYY-MM.', 'error')
    
    attendance_records = query.order_by(Attendance.date.desc()).paginate(
        page=page, per_page=31, error_out=False
    )
    
    return render_template('staff/attendance_history.html',
                         title='Attendance History',
                         attendance_records=attendance_records,
                         month=month,
                         staff=staff)

@bp.route('/leave/request', methods=['GET', 'POST'])
@login_required
@staff_required
def request_leave():
    """Submit leave request"""
    staff = Staff.query.filter_by(user_id=current_user.id).first()
    if not staff:
        flash('Staff profile not found.', 'error')
        return redirect(url_for('main.index'))
    
    form = LeaveRequestForm()
    
    if form.validate_on_submit():
        # Calculate total days
        total_days = (form.end_date.data - form.start_date.data).days + 1
        
        leave_request = LeaveRequest(
            staff_id=staff.id,
            leave_type=form.leave_type.data,
            start_date=form.start_date.data,
            end_date=form.end_date.data,
            total_days=total_days,
            reason=form.reason.data
        )
        
        db.session.add(leave_request)
        db.session.commit()
        
        flash('Leave request submitted successfully!', 'success')
        return redirect(url_for('staff.leave_history'))
    
    return render_template('staff/request_leave.html', title='Request Leave', form=form)

@bp.route('/leave/history')
@login_required
@staff_required
def leave_history():
    """View leave request history"""
    staff = Staff.query.filter_by(user_id=current_user.id).first()
    if not staff:
        flash('Staff profile not found.', 'error')
        return redirect(url_for('main.index'))
    
    page = request.args.get('page', 1, type=int)
    
    leave_requests = LeaveRequest.query.filter_by(staff_id=staff.id).order_by(
        LeaveRequest.applied_date.desc()
    ).paginate(page=page, per_page=10, error_out=False)
    
    return render_template('staff/leave_history.html',
                         title='Leave History',
                         leave_requests=leave_requests,
                         staff=staff)

@bp.route('/profile', methods=['GET', 'POST'])
@login_required
@staff_required
def profile():
    """View and update profile"""
    staff = Staff.query.filter_by(user_id=current_user.id).first()
    if not staff:
        flash('Staff profile not found.', 'error')
        return redirect(url_for('main.index'))
    
    form = ProfileUpdateForm()
    
    if form.validate_on_submit():
        staff.phone = form.phone.data
        staff.address = form.address.data
        staff.updated_at = datetime.utcnow()
        
        db.session.commit()
        flash('Profile updated successfully!', 'success')
        return redirect(url_for('staff.profile'))
    
    # Pre-populate form
    form.phone.data = staff.phone
    form.address.data = staff.address
    
    return render_template('staff/profile.html', title='My Profile', form=form, staff=staff)
