{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-calendar-check"></i> Attendance Records</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.manual_attendance_entry') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Manual Entry
            </a>
            <button type="button" class="btn btn-sm btn-success">
                <i class="fas fa-download"></i> Export
            </button>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <label for="date" class="form-label">Date</label>
                        <input type="date" name="date" id="date" class="form-control" value="{{ date_filter }}">
                    </div>
                    <div class="col-md-4">
                        <label for="staff" class="form-label">Staff</label>
                        <input type="text" name="staff" id="staff" class="form-control" placeholder="Search by name or ID..." value="{{ staff_filter }}">
                    </div>
                    <div class="col-md-3">
                        <label for="status" class="form-label">Status</label>
                        <select name="status" id="status" class="form-select">
                            <option value="">All Status</option>
                            <option value="present">Present</option>
                            <option value="absent">Absent</option>
                            <option value="late">Late</option>
                            <option value="half_day">Half Day</option>
                            <option value="leave">Leave</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Records -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list"></i> Attendance Records</h5>
    </div>
    <div class="card-body p-0">
        {% if attendance_records.items %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Date</th>
                            <th>Staff</th>
                            <th>Check In</th>
                            <th>Check Out</th>
                            <th>Break</th>
                            <th>Total Hours</th>
                            <th>Overtime</th>
                            <th>Status</th>
                            <th>Type</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for attendance in attendance_records.items %}
                        <tr>
                            <td>
                                <strong>{{ attendance.date.strftime('%m/%d/%Y') }}</strong><br>
                                <small class="text-muted">{{ attendance.date.strftime('%A') }}</small>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                        {{ attendance.staff.first_name[0] }}{{ attendance.staff.last_name[0] }}
                                    </div>
                                    <div>
                                        <div class="fw-bold">{{ attendance.staff.full_name }}</div>
                                        <small class="text-muted">{{ attendance.staff.staff_id }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                {% if attendance.check_in_time %}
                                    <span class="text-success">{{ attendance.check_in_time.strftime('%H:%M') }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if attendance.check_out_time %}
                                    <span class="text-warning">{{ attendance.check_out_time.strftime('%H:%M') }}</span>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if attendance.break_start_time and attendance.break_end_time %}
                                    <small>
                                        {{ attendance.break_start_time.strftime('%H:%M') }} - 
                                        {{ attendance.break_end_time.strftime('%H:%M') }}
                                    </small>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <strong>{{ "%.2f"|format(attendance.total_hours) if attendance.total_hours else '0.00' }}</strong>
                            </td>
                            <td>
                                {% if attendance.overtime_hours and attendance.overtime_hours > 0 %}
                                    <span class="text-info">{{ "%.2f"|format(attendance.overtime_hours) }}</span>
                                {% else %}
                                    <span class="text-muted">0.00</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if attendance.status == 'present' else 'warning' if attendance.status == 'late' else 'danger' if attendance.status == 'absent' else 'info' }}">
                                    {{ attendance.status.replace('_', ' ').title() }}
                                </span>
                            </td>
                            <td>
                                {% if attendance.is_manual_entry %}
                                    <span class="badge bg-secondary">Manual</span>
                                {% else %}
                                    <span class="badge bg-primary">Biometric</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-primary" 
                                            title="View Details" onclick="viewDetails({{ attendance.id }})">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                            title="Edit" onclick="editAttendance({{ attendance.id }})">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No attendance records found</h5>
                {% if date_filter or staff_filter %}
                    <p class="text-muted">No results match your current filters.</p>
                    <a href="{{ url_for('admin.attendance_list') }}" class="btn btn-outline-primary">
                        <i class="fas fa-times"></i> Clear Filters
                    </a>
                {% else %}
                    <p class="text-muted">Attendance records will appear here once staff start using the biometric system.</p>
                    <a href="{{ url_for('admin.manual_attendance_entry') }}" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Add Manual Entry
                    </a>
                {% endif %}
            </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if attendance_records.pages > 1 %}
<nav aria-label="Attendance pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if attendance_records.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('admin.attendance_list', page=attendance_records.prev_num, date=date_filter, staff=staff_filter) }}">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
            </li>
        {% endif %}
        
        {% for page_num in attendance_records.iter_pages() %}
            {% if page_num %}
                {% if page_num != attendance_records.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('admin.attendance_list', page=page_num, date=date_filter, staff=staff_filter) }}">
                            {{ page_num }}
                        </a>
                    </li>
                {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if attendance_records.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('admin.attendance_list', page=attendance_records.next_num, date=date_filter, staff=staff_filter) }}">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
function viewDetails(attendanceId) {
    alert('View attendance details for ID: ' + attendanceId);
}

function editAttendance(attendanceId) {
    alert('Edit attendance for ID: ' + attendanceId);
}
</script>

<style>
.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
    font-weight: bold;
}
</style>
{% endblock %}
