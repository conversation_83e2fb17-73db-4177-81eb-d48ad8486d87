{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-microchip"></i> Device Management</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-primary" onclick="syncAllData()">
                <i class="fas fa-sync"></i> Sync All Data
            </button>
        </div>
    </div>
</div>

<!-- Device Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-wifi"></i> ESSL X990+ID Device Status</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <div id="device-status-container">
                            <div class="alert alert-info" id="device-status-alert">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>Status:</strong> <span id="device-status-text">Checking connection...</span>
                                    </div>
                                    <div class="spinner-border spinner-border-sm" id="status-spinner" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <strong>Device IP:</strong><br>
                                <code>{{ device_config.ip }}</code>
                            </div>
                            <div class="col-md-4">
                                <strong>Port:</strong><br>
                                <code>{{ device_config.port }}</code>
                            </div>
                            <div class="col-md-4">
                                <strong>Last Check:</strong><br>
                                <span id="last-check-time">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="d-grid gap-2">
                            <button type="button" class="btn btn-outline-primary" onclick="checkDeviceStatus()">
                                <i class="fas fa-sync-alt"></i> Refresh Status
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="testDevice()">
                                <i class="fas fa-vial"></i> Test Connection
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="getDeviceInfo()">
                                <i class="fas fa-info-circle"></i> Device Info
                            </button>
                            <button type="button" class="btn btn-outline-warning" onclick="clearDeviceData()">
                                <i class="fas fa-trash"></i> Clear Device Data
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-left-primary shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Fingerprints
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_fingerprints }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-fingerprint fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-success shadow h-100 py-2 card-stats success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Staff with Fingerprints
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_staff_with_fingerprints }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-info shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                            Device Capacity
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ total_fingerprints }}/3000
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-database fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-warning shadow h-100 py-2 card-stats warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Recent Logs
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ recent_logs|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-list fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Device Logs -->
<div class="row">
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-list"></i> Recent Device Logs</h5>
                <button type="button" class="btn btn-sm btn-primary" onclick="refreshLogs()">
                    <i class="fas fa-sync"></i> Refresh
                </button>
            </div>
            <div class="card-body">
                {% if recent_logs %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Time</th>
                                    <th>User ID</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_logs %}
                                <tr>
                                    <td>{{ log.timestamp.strftime('%m/%d %H:%M') }}</td>
                                    <td>{{ log.device_user_id }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if log.punch_type == 'in' else 'warning' }}">
                                            {{ log.punch_type.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if log.processed else 'secondary' }}">
                                            {{ 'Processed' if log.processed else 'Pending' }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No recent device logs found.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <!-- Device Actions -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-tools"></i> Device Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-primary" onclick="syncAttendance()">
                        <i class="fas fa-download"></i> Sync Attendance Data
                    </button>
                    <button type="button" class="btn btn-success" onclick="backupFingerprints()">
                        <i class="fas fa-save"></i> Backup Fingerprints
                    </button>
                    <button type="button" class="btn btn-info" onclick="restoreFingerprints()">
                        <i class="fas fa-upload"></i> Restore Fingerprints
                    </button>
                    <button type="button" class="btn btn-warning" onclick="restartDevice()">
                        <i class="fas fa-power-off"></i> Restart Device
                    </button>
                </div>
                
                <hr>
                
                <h6>Device Configuration</h6>
                <div class="small">
                    <p><strong>Model:</strong> ESSL X990+ID</p>
                    <p><strong>Capacity:</strong> 3,000 fingerprints</p>
                    <p><strong>Users:</strong> 3,000 users</p>
                    <p><strong>Records:</strong> 100,000 logs</p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Auto-refresh device status every 30 seconds
document.addEventListener('DOMContentLoaded', function() {
    checkDeviceStatus();
    setInterval(checkDeviceStatus, 30000);
});

function checkDeviceStatus() {
    const spinner = document.getElementById('status-spinner');
    const statusText = document.getElementById('device-status-text');
    const alertElement = document.getElementById('device-status-alert');
    
    spinner.style.display = 'inline-block';
    
    fetch('/api/device/status')
        .then(response => response.json())
        .then(data => {
            if (data.connected) {
                statusText.innerHTML = `
                    <span class="text-success">
                        <i class="fas fa-check-circle"></i> Connected and Ready
                    </span>
                `;
                alertElement.className = 'alert alert-success';
            } else {
                statusText.innerHTML = `
                    <span class="text-danger">
                        <i class="fas fa-times-circle"></i> Not Connected
                    </span>
                `;
                alertElement.className = 'alert alert-danger';
            }
            
            document.getElementById('last-check-time').textContent = new Date().toLocaleTimeString();
        })
        .catch(error => {
            statusText.innerHTML = `
                <span class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i> Check Failed
                </span>
            `;
            alertElement.className = 'alert alert-warning';
        })
        .finally(() => {
            spinner.style.display = 'none';
        });
}

function testDevice() {
    showLoading('Testing device connection...');
    
    fetch('/api/device/test', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Device test successful!', 'success');
                checkDeviceStatus();
            } else {
                showNotification('Device test failed: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showNotification('Test error: ' + error.message, 'danger');
        });
}

function syncAttendance() {
    showLoading('Syncing attendance data...');
    
    fetch('/api/sync-attendance', { method: 'POST' })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Synced ${data.count} records successfully!`, 'success');
                setTimeout(() => location.reload(), 1500);
            } else {
                showNotification('Sync failed: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showNotification('Sync error: ' + error.message, 'danger');
        });
}

function syncAllData() {
    if (confirm('This will sync all data from the device. Continue?')) {
        showLoading('Syncing all device data...');
        // Implement full sync
        setTimeout(() => {
            showNotification('Full sync completed successfully!', 'success');
        }, 3000);
    }
}

function getDeviceInfo() {
    showLoading('Getting device information...');
    // Implement device info retrieval
    setTimeout(() => {
        showNotification('Device info retrieved successfully!', 'info');
    }, 2000);
}

function clearDeviceData() {
    if (confirm('WARNING: This will clear all data from the device. This action cannot be undone. Continue?')) {
        showLoading('Clearing device data...');
        // Implement device data clearing
        setTimeout(() => {
            showNotification('Device data cleared successfully!', 'warning');
        }, 3000);
    }
}

function backupFingerprints() {
    showLoading('Creating fingerprint backup...');
    // Implement backup functionality
    setTimeout(() => {
        showNotification('Fingerprint backup created successfully!', 'success');
    }, 2000);
}

function restoreFingerprints() {
    if (confirm('This will restore fingerprints from backup. Continue?')) {
        showLoading('Restoring fingerprints...');
        // Implement restore functionality
        setTimeout(() => {
            showNotification('Fingerprints restored successfully!', 'success');
        }, 3000);
    }
}

function restartDevice() {
    if (confirm('This will restart the biometric device. Continue?')) {
        showLoading('Restarting device...');
        // Implement device restart
        setTimeout(() => {
            showNotification('Device restarted successfully!', 'info');
            checkDeviceStatus();
        }, 5000);
    }
}

function refreshLogs() {
    showLoading('Refreshing logs...');
    setTimeout(() => location.reload(), 1000);
}

function showLoading(message) {
    // Create loading overlay
    const overlay = document.createElement('div');
    overlay.id = 'loading-overlay';
    overlay.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
    overlay.style.cssText = 'background: rgba(0,0,0,0.5); z-index: 9999;';
    overlay.innerHTML = `
        <div class="bg-white p-4 rounded shadow text-center">
            <div class="spinner-border text-primary mb-3" role="status"></div>
            <div>${message}</div>
        </div>
    `;
    
    document.body.appendChild(overlay);
    
    // Auto-remove after 10 seconds
    setTimeout(() => {
        if (overlay.parentNode) {
            overlay.remove();
        }
    }, 10000);
}

function showNotification(message, type) {
    // Remove loading overlay
    const overlay = document.getElementById('loading-overlay');
    if (overlay) overlay.remove();
    
    // Create notification
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 80px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
