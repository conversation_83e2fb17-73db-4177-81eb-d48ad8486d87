from flask_wtf import FlaskForm
from wtforms import <PERSON><PERSON><PERSON>, TextAreaField, SelectField, DateField, DecimalField, BooleanField, SubmitField, IntegerField
from wtforms.validators import DataRequired, Email, Length, NumberRange, Optional
from wtforms.widgets import TextArea
from datetime import date

class StaffRegistrationForm(FlaskForm):
    # Personal Information
    staff_id = StringField('Staff ID', validators=[DataRequired(), Length(min=3, max=20)])
    first_name = StringField('First Name', validators=[DataRequired(), Length(max=50)])
    last_name = StringField('Last Name', validators=[DataRequired(), Length(max=50)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    phone = StringField('Phone', validators=[Length(max=15)])
    date_of_birth = DateField('Date of Birth', validators=[Optional()])
    gender = <PERSON><PERSON><PERSON>('Gender', choices=[('', 'Select Gender'), ('male', 'Male'), ('female', 'Female'), ('other', 'Other')])
    address = TextAreaField('Address', widget=TextArea())
    
    # Employment Information
    department = StringField('Department', validators=[Length(max=100)])
    designation = StringField('Designation', validators=[Length(max=100)])
    date_of_joining = DateField('Date of Joining', validators=[DataRequired()], default=date.today)
    employee_type = SelectField('Employee Type', 
                               choices=[('full_time', 'Full Time'), ('part_time', 'Part Time'), ('contract', 'Contract')],
                               default='full_time')
    salary = DecimalField('Salary', validators=[Optional(), NumberRange(min=0)], places=2)
    
    # Status
    is_active = BooleanField('Active', default=True)
    
    submit = SubmitField('Register Staff')

class AttendanceEntryForm(FlaskForm):
    staff_id = SelectField('Staff', coerce=int, validators=[DataRequired()])
    date = DateField('Date', validators=[DataRequired()], default=date.today)
    check_in_time = StringField('Check In Time (HH:MM)', validators=[Optional()])
    check_out_time = StringField('Check Out Time (HH:MM)', validators=[Optional()])
    break_start_time = StringField('Break Start Time (HH:MM)', validators=[Optional()])
    break_end_time = StringField('Break End Time (HH:MM)', validators=[Optional()])
    status = SelectField('Status', 
                        choices=[('present', 'Present'), ('absent', 'Absent'), ('late', 'Late'), 
                                ('half_day', 'Half Day'), ('leave', 'Leave')],
                        default='present')
    notes = TextAreaField('Notes')
    submit = SubmitField('Save Attendance')

class LeaveApprovalForm(FlaskForm):
    status = SelectField('Status', 
                        choices=[('approved', 'Approve'), ('rejected', 'Reject')],
                        validators=[DataRequired()])
    admin_comments = TextAreaField('Admin Comments')
    submit = SubmitField('Update Leave Request')

class FingerprintRegistrationForm(FlaskForm):
    staff_id = SelectField('Staff', coerce=int, validators=[DataRequired()])
    finger_id = SelectField('Finger', 
                           choices=[(0, 'Right Thumb'), (1, 'Right Index'), (2, 'Right Middle'), 
                                   (3, 'Right Ring'), (4, 'Right Little'), (5, 'Left Thumb'), 
                                   (6, 'Left Index'), (7, 'Left Middle'), (8, 'Left Ring'), 
                                   (9, 'Left Little')],
                           coerce=int, validators=[DataRequired()])
    device_user_id = IntegerField('Device User ID', validators=[DataRequired(), NumberRange(min=1, max=9999)])
    submit = SubmitField('Register Fingerprint')
