{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-user"></i> {{ staff.full_name }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.staff_list') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Staff List
            </a>
            <a href="{{ url_for('admin.staff_fingerprints', staff_id=staff.id) }}" class="btn btn-sm btn-success">
                <i class="fas fa-fingerprint"></i> Fingerprints
            </a>
            <button type="button" class="btn btn-sm btn-primary">
                <i class="fas fa-edit"></i> Edit Staff
            </button>
        </div>
    </div>
</div>

<div class="row">
    <!-- Staff Information -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-id-card"></i> Staff Information</h5>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <div class="avatar-lg bg-primary text-white rounded-circle d-inline-flex align-items-center justify-content-center mb-2">
                        <span class="h3 mb-0">{{ staff.first_name[0] }}{{ staff.last_name[0] }}</span>
                    </div>
                    <h5 class="mb-1">{{ staff.full_name }}</h5>
                    <p class="text-muted mb-0">{{ staff.staff_id }}</p>
                    <span class="badge bg-{{ 'success' if staff.is_active else 'secondary' }} mt-1">
                        {{ 'Active' if staff.is_active else 'Inactive' }}
                    </span>
                </div>
                
                <hr>
                
                <div class="row g-2">
                    <div class="col-12">
                        <strong>Email:</strong><br>
                        <span class="text-muted">{{ staff.email }}</span>
                    </div>
                    {% if staff.phone %}
                    <div class="col-12 mt-2">
                        <strong>Phone:</strong><br>
                        <span class="text-muted">{{ staff.phone }}</span>
                    </div>
                    {% endif %}
                    {% if staff.date_of_birth %}
                    <div class="col-12 mt-2">
                        <strong>Date of Birth:</strong><br>
                        <span class="text-muted">{{ staff.date_of_birth.strftime('%B %d, %Y') }}</span>
                    </div>
                    {% endif %}
                    {% if staff.gender %}
                    <div class="col-12 mt-2">
                        <strong>Gender:</strong><br>
                        <span class="text-muted">{{ staff.gender.title() }}</span>
                    </div>
                    {% endif %}
                    {% if staff.address %}
                    <div class="col-12 mt-2">
                        <strong>Address:</strong><br>
                        <span class="text-muted">{{ staff.address }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Employment Information -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-briefcase"></i> Employment Details</h5>
            </div>
            <div class="card-body">
                <div class="row g-2">
                    {% if staff.department %}
                    <div class="col-12">
                        <strong>Department:</strong><br>
                        <span class="badge bg-info">{{ staff.department }}</span>
                    </div>
                    {% endif %}
                    {% if staff.designation %}
                    <div class="col-12 mt-2">
                        <strong>Designation:</strong><br>
                        <span class="text-muted">{{ staff.designation }}</span>
                    </div>
                    {% endif %}
                    <div class="col-12 mt-2">
                        <strong>Date of Joining:</strong><br>
                        <span class="text-muted">{{ staff.date_of_joining.strftime('%B %d, %Y') }}</span>
                    </div>
                    <div class="col-12 mt-2">
                        <strong>Employee Type:</strong><br>
                        <span class="text-muted">{{ staff.employee_type.replace('_', ' ').title() }}</span>
                    </div>
                    {% if staff.salary %}
                    <div class="col-12 mt-2">
                        <strong>Salary:</strong><br>
                        <span class="text-muted">${{ "%.2f"|format(staff.salary) }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
    
    <!-- Recent Attendance -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-calendar-check"></i> Recent Attendance</h5>
                <a href="{{ url_for('admin.manual_attendance_entry') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> Add Entry
                </a>
            </div>
            <div class="card-body">
                {% if recent_attendance %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Check In</th>
                                    <th>Check Out</th>
                                    <th>Hours</th>
                                    <th>Status</th>
                                    <th>Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendance in recent_attendance %}
                                <tr>
                                    <td>{{ attendance.date.strftime('%m/%d/%Y') }}</td>
                                    <td>{{ attendance.check_in_time.strftime('%H:%M') if attendance.check_in_time else '-' }}</td>
                                    <td>{{ attendance.check_out_time.strftime('%H:%M') if attendance.check_out_time else '-' }}</td>
                                    <td>{{ "%.2f"|format(attendance.total_hours) if attendance.total_hours else '0.00' }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if attendance.status == 'present' else 'warning' if attendance.status == 'late' else 'danger' }}">
                                            {{ attendance.status.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        {% if attendance.is_manual_entry %}
                                            <span class="badge bg-secondary">Manual</span>
                                        {% else %}
                                            <span class="badge bg-primary">Biometric</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No attendance records found.</p>
                    </div>
                {% endif %}
            </div>
        </div>
        
        <!-- Leave Requests -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-times"></i> Leave Requests</h5>
            </div>
            <div class="card-body">
                {% if leave_requests %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Type</th>
                                    <th>Dates</th>
                                    <th>Days</th>
                                    <th>Status</th>
                                    <th>Applied</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for leave in leave_requests %}
                                <tr>
                                    <td>
                                        <span class="badge bg-info">{{ leave.leave_type.title() }}</span>
                                    </td>
                                    <td>{{ leave.start_date.strftime('%m/%d') }} - {{ leave.end_date.strftime('%m/%d') }}</td>
                                    <td>{{ leave.total_days }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'warning' if leave.status == 'pending' else 'success' if leave.status == 'approved' else 'danger' }}">
                                            {{ leave.status.title() }}
                                        </span>
                                    </td>
                                    <td>{{ leave.applied_date.strftime('%m/%d/%Y') }}</td>
                                    <td>
                                        {% if leave.status == 'pending' %}
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-success" onclick="approveLeave({{ leave.id }})">
                                                    <i class="fas fa-check"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" onclick="rejectLeave({{ leave.id }})">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </div>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-check fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No leave requests found.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function approveLeave(leaveId) {
    if (confirm('Are you sure you want to approve this leave request?')) {
        // Implement leave approval logic
        alert('Leave approved for ID: ' + leaveId);
    }
}

function rejectLeave(leaveId) {
    if (confirm('Are you sure you want to reject this leave request?')) {
        // Implement leave rejection logic
        alert('Leave rejected for ID: ' + leaveId);
    }
}
</script>

<style>
.avatar-lg {
    width: 80px;
    height: 80px;
    font-size: 24px;
}
</style>
{% endblock %}
