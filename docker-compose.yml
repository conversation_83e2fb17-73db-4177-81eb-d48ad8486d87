version: '3.8'

services:
  web:
    build: .
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=mysql+pymysql://staff_user:staff_password@db:3306/staff_management
      - SECRET_KEY=your-production-secret-key-here
      - ESSL_DEVICE_IP=*************
      - ESSL_DEVICE_PORT=4370
    depends_on:
      - db
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped

  db:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root_password
      - MYSQL_DATABASE=staff_management
      - MYSQL_USER=staff_user
      - MYSQL_PASSWORD=staff_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  scheduler:
    build: .
    command: python scheduler.py
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=mysql+pymysql://staff_user:staff_password@db:3306/staff_management
      - SECRET_KEY=your-production-secret-key-here
      - ESSL_DEVICE_IP=*************
      - ESSL_DEVICE_PORT=4370
    depends_on:
      - db
    restart: unless-stopped

volumes:
  mysql_data:
