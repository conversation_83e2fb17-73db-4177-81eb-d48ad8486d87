from flask import render_template, redirect, url_for
from flask_login import current_user
from app.main import bp

@bp.route('/')
@bp.route('/index')
def index():
    """Home page - redirect based on user role"""
    if current_user.is_authenticated:
        if current_user.is_admin():
            return redirect(url_for('admin.dashboard'))
        else:
            return redirect(url_for('staff.dashboard'))
    return render_template('main/index.html', title='Staff Management System')

@bp.route('/about')
def about():
    """About page"""
    return render_template('main/about.html', title='About')
