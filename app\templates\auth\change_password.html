{% extends "base.html" %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow mt-5">
            <div class="card-header bg-warning text-dark text-center">
                <h4><i class="fas fa-key"></i> Change Password</h4>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="mb-3">
                        {{ form.current_password.label(class="form-label") }}
                        {{ form.current_password(class="form-control") }}
                        {% if form.current_password.errors %}
                            <div class="text-danger">
                                {% for error in form.current_password.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.new_password.label(class="form-label") }}
                        {{ form.new_password(class="form-control") }}
                        {% if form.new_password.errors %}
                            <div class="text-danger">
                                {% for error in form.new_password.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="mb-3">
                        {{ form.new_password2.label(class="form-label") }}
                        {{ form.new_password2(class="form-control") }}
                        {% if form.new_password2.errors %}
                            <div class="text-danger">
                                {% for error in form.new_password2.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('main.index') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        {{ form.submit(class="btn btn-warning") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
