from datetime import datetime, date
from decimal import Decimal
from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import Numeric
from app import db, login_manager

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

class User(UserMixin, db.Model):
    """User model for authentication"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='staff')  # 'admin' or 'staff'
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationship with staff
    staff = db.relationship('Staff', backref='user', uselist=False)
    
    def set_password(self, password):
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        return check_password_hash(self.password_hash, password)
    
    def is_admin(self):
        return self.role == 'admin'
    
    def __repr__(self):
        return f'<User {self.username}>'

class Staff(db.Model):
    """Staff information model"""
    id = db.Column(db.Integer, primary_key=True)
    staff_id = db.Column(db.String(20), unique=True, nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    
    # Personal Information
    first_name = db.Column(db.String(50), nullable=False)
    last_name = db.Column(db.String(50), nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    phone = db.Column(db.String(15))
    date_of_birth = db.Column(db.Date)
    gender = db.Column(db.String(10))
    address = db.Column(db.Text)
    
    # Employment Information
    department = db.Column(db.String(100))
    designation = db.Column(db.String(100))
    date_of_joining = db.Column(db.Date, nullable=False)
    employee_type = db.Column(db.String(20), default='full_time')  # full_time, part_time, contract
    salary = db.Column(Numeric(10, 2))
    
    # Status
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    attendance_records = db.relationship('Attendance', backref='staff', lazy='dynamic')
    leave_requests = db.relationship('LeaveRequest', backref='staff', lazy='dynamic')
    fingerprint_templates = db.relationship('FingerprintTemplate', backref='staff', lazy='dynamic')
    
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}"
    
    def __repr__(self):
        return f'<Staff {self.staff_id}: {self.full_name}>'

class FingerprintTemplate(db.Model):
    """Fingerprint template storage"""
    id = db.Column(db.Integer, primary_key=True)
    staff_id = db.Column(db.Integer, db.ForeignKey('staff.id'), nullable=False)
    finger_id = db.Column(db.Integer, nullable=False)  # 0-9 for different fingers
    template_data = db.Column(db.LargeBinary)  # Encrypted fingerprint template
    device_user_id = db.Column(db.Integer)  # ID used in biometric device
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    __table_args__ = (db.UniqueConstraint('staff_id', 'finger_id'),)
    
    def __repr__(self):
        return f'<FingerprintTemplate Staff:{self.staff_id} Finger:{self.finger_id}>'

class Attendance(db.Model):
    """Daily attendance records"""
    id = db.Column(db.Integer, primary_key=True)
    staff_id = db.Column(db.Integer, db.ForeignKey('staff.id'), nullable=False)
    date = db.Column(db.Date, nullable=False)

    # Time tracking
    check_in_time = db.Column(db.DateTime)
    check_out_time = db.Column(db.DateTime)
    break_start_time = db.Column(db.DateTime)
    break_end_time = db.Column(db.DateTime)

    # Status and calculations
    status = db.Column(db.String(20), default='present')  # present, absent, late, half_day, leave
    total_hours = db.Column(db.Float, default=0.0)
    overtime_hours = db.Column(db.Float, default=0.0)
    is_manual_entry = db.Column(db.Boolean, default=False)

    # Additional information
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    __table_args__ = (db.UniqueConstraint('staff_id', 'date'),)

    def calculate_total_hours(self):
        """Calculate total working hours"""
        if self.check_in_time and self.check_out_time:
            total_time = self.check_out_time - self.check_in_time

            # Subtract break time if available
            if self.break_start_time and self.break_end_time:
                break_time = self.break_end_time - self.break_start_time
                total_time -= break_time

            self.total_hours = total_time.total_seconds() / 3600  # Convert to hours

            # Calculate overtime (assuming 8 hours is standard)
            if self.total_hours > 8:
                self.overtime_hours = self.total_hours - 8
            else:
                self.overtime_hours = 0.0

    def __repr__(self):
        return f'<Attendance {self.staff.staff_id} on {self.date}>'

class LeaveRequest(db.Model):
    """Leave request management"""
    id = db.Column(db.Integer, primary_key=True)
    staff_id = db.Column(db.Integer, db.ForeignKey('staff.id'), nullable=False)

    # Leave details
    leave_type = db.Column(db.String(20), nullable=False)  # sick, casual, annual, maternity, etc.
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    total_days = db.Column(db.Integer, nullable=False)

    # Request information
    reason = db.Column(db.Text, nullable=False)
    status = db.Column(db.String(20), default='pending')  # pending, approved, rejected
    applied_date = db.Column(db.DateTime, default=datetime.utcnow)

    # Approval information
    approved_by = db.Column(db.Integer, db.ForeignKey('user.id'))
    approved_date = db.Column(db.DateTime)
    admin_comments = db.Column(db.Text)

    # Relationships
    approver = db.relationship('User', foreign_keys=[approved_by])

    def __repr__(self):
        return f'<LeaveRequest {self.staff.staff_id}: {self.start_date} to {self.end_date}>'

class AttendanceLog(db.Model):
    """Raw attendance logs from biometric device"""
    id = db.Column(db.Integer, primary_key=True)
    device_user_id = db.Column(db.Integer, nullable=False)
    timestamp = db.Column(db.DateTime, nullable=False)
    punch_type = db.Column(db.String(10), default='unknown')  # in, out, break_start, break_end
    device_id = db.Column(db.String(50))
    raw_data = db.Column(db.Text)  # Store raw device response
    processed = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def __repr__(self):
        return f'<AttendanceLog User:{self.device_user_id} at {self.timestamp}>'
