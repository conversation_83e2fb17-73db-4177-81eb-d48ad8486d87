#!/usr/bin/env python3
"""
ESSL X990+ID Device Setup and Configuration Helper
"""

import socket
import struct
import time
import os
from datetime import datetime

class DeviceSetupHelper:
    """Helper class for ESSL device setup and configuration"""
    
    def __init__(self):
        self.default_port = 4370
        self.timeout = 5
    
    def get_local_networks(self):
        """Get all local network ranges to scan"""
        networks = []

        try:
            import netifaces
            # Get all network interfaces
            for interface in netifaces.interfaces():
                addrs = netifaces.ifaddresses(interface)
                if netifaces.AF_INET in addrs:
                    for addr_info in addrs[netifaces.AF_INET]:
                        ip = addr_info.get('addr')
                        netmask = addr_info.get('netmask')

                        if ip and netmask and not ip.startswith('127.'):
                            # Calculate network range
                            network_base = '.'.join(ip.split('.')[:-1])
                            if network_base not in networks:
                                networks.append(network_base)
        except ImportError:
            print("📦 netifaces not installed - using common network ranges")
            # Fallback to common networks if netifaces not available
            networks = ["192.168.1", "192.168.0", "10.0.0", "172.16.0"]
        except Exception as e:
            print(f"⚠️ Error getting network interfaces: {e}")
            networks = ["192.168.1", "192.168.0"]

        # If no networks found, use defaults
        if not networks:
            networks = ["192.168.1", "192.168.0", "10.0.0"]

        return networks

    def scan_network_for_devices(self, network_base="192.168.1", start=1, end=254, max_threads=50):
        """Scan network for ESSL devices using threading for faster scanning"""
        import threading
        import queue

        print(f"Scanning network {network_base}.{start}-{end} for ESSL devices...")
        found_devices = []
        device_queue = queue.Queue()

        def scan_ip(ip):
            device_info = self.test_device_connection_detailed(ip, verbose=False)
            if device_info:
                device_queue.put(device_info)

        # Create and start threads
        threads = []
        for i in range(start, end + 1):
            ip = f"{network_base}.{i}"
            thread = threading.Thread(target=scan_ip, args=(ip,))
            threads.append(thread)
            thread.start()

            # Limit concurrent threads
            if len(threads) >= max_threads:
                for t in threads:
                    t.join()
                threads = []

        # Wait for remaining threads
        for thread in threads:
            thread.join()

        # Collect results
        while not device_queue.empty():
            device_info = device_queue.get()
            found_devices.append(device_info)
            print(f"✓ Found ESSL device at {device_info['ip']}:{device_info['port']} - {device_info['model']}")

        return found_devices

    def scan_all_networks(self):
        """Scan all local networks for devices"""
        print("Scanning all local networks for biometric devices...")
        all_devices = []

        networks = self.get_local_networks()
        print(f"Found {len(networks)} local networks to scan: {networks}")

        for network in networks:
            devices = self.scan_network_for_devices(network, 1, 254)
            all_devices.extend(devices)

        return all_devices
    
    def test_device_connection(self, ip, port=None, verbose=True):
        """Test connection to a specific device"""
        if port is None:
            port = self.default_port

        if verbose:
            print(f"Testing connection to {ip}:{port}...")

        try:
            # Create UDP socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(self.timeout)

            # Create connection command
            command = self._create_command(1000, b'')  # CMD_CONNECT

            # Send command
            sock.sendto(command, (ip, port))

            # Wait for response
            data, addr = sock.recvfrom(1024)
            sock.close()

            if len(data) >= 8:
                reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
                if command_id == 2000:  # CMD_ACK_OK
                    if verbose:
                        print(f"✓ Successfully connected to device at {ip}:{port}")
                    return True

            if verbose:
                print(f"✗ Device at {ip}:{port} responded but connection failed")
            return False

        except socket.timeout:
            if verbose:
                print(f"✗ Connection timeout to {ip}:{port}")
            return False
        except Exception as e:
            if verbose:
                print(f"✗ Connection error to {ip}:{port}: {str(e)}")
            return False

    def test_device_connection_detailed(self, ip, port=None, verbose=True):
        """Test connection and get detailed device information"""
        if port is None:
            port = self.default_port

        try:
            # Create UDP socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(self.timeout)

            # Create connection command
            command = self._create_command(1000, b'')  # CMD_CONNECT

            # Send command
            sock.sendto(command, (ip, port))

            # Wait for response
            data, addr = sock.recvfrom(1024)

            if len(data) >= 8:
                reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
                if command_id == 2000:  # CMD_ACK_OK
                    device_info = {
                        'ip': ip,
                        'port': port,
                        'session_id': session_id,
                        'model': 'ESSL X990+ID',
                        'status': 'connected',
                        'response_time': self.timeout,
                        'firmware': 'Unknown',
                        'serial': 'Unknown'
                    }

                    # Try to get additional device info
                    try:
                        # Get device version/info
                        info_command = self._create_command(50, b'', session_id)  # CMD_GET_VERSION
                        sock.sendto(info_command, (ip, port))
                        info_data, addr = sock.recvfrom(1024)

                        if len(info_data) > 8:
                            # Parse version info if available
                            version_info = info_data[8:].decode('ascii', errors='ignore').strip()
                            if version_info:
                                device_info['firmware'] = version_info[:20]
                    except:
                        pass

                    # Disconnect
                    disconnect_command = self._create_command(1001, b'', session_id)
                    sock.sendto(disconnect_command, (ip, port))

                    sock.close()
                    return device_info

            sock.close()
            return None

        except socket.timeout:
            return None
        except Exception as e:
            return None
    
    def get_device_info(self, ip, port=None):
        """Get detailed information from device"""
        if port is None:
            port = self.default_port
        
        print(f"Getting device information from {ip}:{port}...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(self.timeout)
            
            # Connect first
            command = self._create_command(1000, b'')
            sock.sendto(command, (ip, port))
            data, addr = sock.recvfrom(1024)
            
            if len(data) >= 8:
                reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
                if command_id == 2000:
                    print(f"✓ Connected to device (Session ID: {session_id})")
                    
                    # Get device time as a test
                    time_command = self._create_command(11, b'', session_id)
                    sock.sendto(time_command, (ip, port))
                    
                    try:
                        time_data, addr = sock.recvfrom(1024)
                        print(f"✓ Device responded to time request")
                    except socket.timeout:
                        print("⚠ Device time request timed out")
                    
                    # Disconnect
                    disconnect_command = self._create_command(1001, b'', session_id)
                    sock.sendto(disconnect_command, (ip, port))
                    
                    sock.close()
                    return True
            
            sock.close()
            return False
            
        except Exception as e:
            print(f"✗ Error getting device info: {str(e)}")
            return False
    
    def _create_command(self, command_id, data, session_id=0):
        """Create command packet for device communication"""
        reply_id = 1
        checksum = sum(data) & 0xFFFF if data else 0
        header = struct.pack('<HHHH', reply_id, command_id, checksum, session_id)
        return header + data
    
    def create_env_file(self, device_ip):
        """Create .env file with device configuration"""
        env_content = f"""# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=sqlite:///staff_management.db

# ESSL X990+ID Biometric Device Configuration
ESSL_DEVICE_IP={device_ip}
ESSL_DEVICE_PORT=4370
ESSL_DEVICE_PASSWORD=0

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216
"""

        with open('.env', 'w') as f:
            f.write(env_content)

        print(f"✓ Created .env file with device IP: {device_ip}")

    def create_env_file_multi(self, devices, primary_device):
        """Create .env file with multiple device configuration"""
        # Create device list for configuration
        device_ips = [device['ip'] for device in devices]
        device_list = ','.join(device_ips)

        env_content = f"""# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-{datetime.now().strftime('%Y%m%d')}

# Database Configuration
DATABASE_URL=sqlite:///staff_management.db

# Primary ESSL X990+ID Biometric Device Configuration
ESSL_DEVICE_IP={primary_device['ip']}
ESSL_DEVICE_PORT={primary_device['port']}
ESSL_DEVICE_PASSWORD=0

# Multiple Device Support (comma-separated IPs)
ESSL_DEVICE_LIST={device_list}
ESSL_DEVICE_COUNT={len(devices)}

# Device Auto-Detection Settings
AUTO_DETECT_DEVICES=true
DEVICE_SCAN_INTERVAL=300
DEVICE_TIMEOUT=5

# Email Configuration
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216
"""

        # Add device details as comments
        env_content += "\n# Detected Device Details:\n"
        for i, device in enumerate(devices, 1):
            env_content += f"# Device {i}: {device['ip']}:{device['port']} - {device['model']}\n"

        with open('.env', 'w') as f:
            f.write(env_content)

        print(f"✓ Created .env file with {len(devices)} device(s)")
        print(f"  Primary device: {primary_device['ip']}")
        print(f"  All devices: {device_list}")

    def create_env_file_multi(self, devices, primary_device):
        """Create .env file with multiple device configuration"""
        # Create device list for configuration
        device_ips = [device['ip'] for device in devices]
        device_list = ','.join(device_ips)

        env_content = f"""# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here-{datetime.now().strftime('%Y%m%d')}

# Database Configuration
DATABASE_URL=sqlite:///staff_management.db

# Primary ESSL X990+ID Biometric Device Configuration
ESSL_DEVICE_IP={primary_device['ip']}
ESSL_DEVICE_PORT={primary_device['port']}
ESSL_DEVICE_PASSWORD=0

# Multiple Device Support (comma-separated IPs)
ESSL_DEVICE_LIST={device_list}
ESSL_DEVICE_COUNT={len(devices)}

# Device Auto-Detection Settings
AUTO_DETECT_DEVICES=true
DEVICE_SCAN_INTERVAL=300  # Scan for new devices every 5 minutes
DEVICE_TIMEOUT=5          # Connection timeout in seconds

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/staff_management.log

# Auto-generated device configuration
# Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
# Detected devices: {len(devices)}
"""

        # Add device details as comments
        env_content += "\n# Detected Device Details:\n"
        for i, device in enumerate(devices, 1):
            env_content += f"# Device {i}: {device['ip']}:{device['port']} - {device['model']} (Session: {device['session_id']})\n"

        with open('.env', 'w') as f:
            f.write(env_content)

        print(f"✓ Created .env file with {len(devices)} device(s)")
        print(f"  Primary device: {primary_device['ip']}")
        print(f"  All devices: {device_list}")

def main():
    """Main setup function"""
    print("=" * 70)
    print("🔍 ESSL Biometric Device Auto-Detection System")
    print("=" * 70)

    helper = DeviceSetupHelper()

    print("\n🔍 Phase 1: Testing common device IPs...")
    common_ips = [
        "*************", "*************", "*************", "*************",
        "*************", "*************", "*************", "*************",
        "**********", "**********", "**********", "**********",
        "************", "************"
    ]

    found_devices = []
    for ip in common_ips:
        device_info = helper.test_device_connection_detailed(ip, verbose=False)
        if device_info:
            found_devices.append(device_info)
            print(f"✓ Found device at {ip}")

    if not found_devices:
        print("\n🌐 Phase 2: Scanning all local networks...")
        found_devices = helper.scan_all_networks()

    if found_devices:
        print(f"\n🎉 SUCCESS: Found {len(found_devices)} biometric device(s)!")
        print("=" * 50)

        for i, device in enumerate(found_devices, 1):
            print(f"\n📱 Device {i}:")
            print(f"   IP Address: {device['ip']}")
            print(f"   Port: {device['port']}")
            print(f"   Model: {device['model']}")
            print(f"   Status: {device['status']}")
            print(f"   Firmware: {device['firmware']}")
            print(f"   Session ID: {device['session_id']}")

        # Select primary device
        if len(found_devices) == 1:
            primary_device = found_devices[0]
            print(f"\n✅ Using device at {primary_device['ip']} as primary device")
        else:
            print(f"\n🔧 Multiple devices found. Select primary device:")
            for i, device in enumerate(found_devices, 1):
                print(f"   {i}. {device['ip']} - {device['model']}")

            while True:
                try:
                    choice = input(f"\nEnter choice (1-{len(found_devices)}): ").strip()
                    if choice:
                        idx = int(choice) - 1
                        if 0 <= idx < len(found_devices):
                            primary_device = found_devices[idx]
                            break
                    else:
                        primary_device = found_devices[0]  # Default to first
                        break
                except ValueError:
                    print("Invalid choice. Please enter a number.")

        # Test primary device in detail
        print(f"\n🔍 Phase 3: Testing primary device {primary_device['ip']} in detail...")
        helper.get_device_info(primary_device['ip'])

        # Create configuration
        print(f"\n⚙️ Phase 4: Creating system configuration...")
        helper.create_env_file_multi(found_devices, primary_device)

        print(f"\n🎉 SETUP COMPLETED SUCCESSFULLY!")
        print("=" * 50)
        print(f"✅ Primary device: {primary_device['ip']}")
        print(f"✅ Total devices detected: {len(found_devices)}")
        print(f"✅ Configuration file created: .env")
        print(f"\n🚀 Next steps:")
        print(f"   1. Run: python app.py")
        print(f"   2. Login as admin (admin/admin123)")
        print(f"   3. Go to Admin → Device Management")
        print(f"   4. Test device connections")
        print(f"   5. Register staff fingerprints")

    else:
        print("\n❌ No biometric devices found on the network.")
        print("\n🔧 Troubleshooting Guide:")
        print("=" * 30)
        print("1. 🔌 Check device power and network connection")
        print("2. 🌐 Verify device IP in device menu: Menu → Comm → Ethernet")
        print("3. 🏠 Ensure device and computer are on same network")
        print("4. 🛡️ Check Windows Firewall settings")
        print("5. 📡 Try connecting device via USB first")
        print("6. 🔄 Restart device and try again")

        print("\n🔧 Manual Configuration:")
        manual_ip = input("\nEnter device IP manually (or press Enter to skip): ").strip()
        if manual_ip:
            device_info = helper.test_device_connection_detailed(manual_ip)
            if device_info:
                helper.create_env_file_multi([device_info], device_info)
                print(f"✅ Configuration created with IP: {manual_ip}")
            else:
                print(f"❌ Could not connect to {manual_ip}")
                print("Please check the IP address and try again.")

if __name__ == '__main__':
    main()
