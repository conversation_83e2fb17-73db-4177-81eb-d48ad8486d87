#!/usr/bin/env python3
"""
ESSL X990+ID Device Setup and Configuration Helper
"""

import socket
import struct
import time
import os
from datetime import datetime

class DeviceSetupHelper:
    """Helper class for ESSL device setup and configuration"""
    
    def __init__(self):
        self.default_port = 4370
        self.timeout = 5
    
    def scan_network_for_devices(self, network_base="192.168.1", start=1, end=254):
        """Scan network for ESSL devices"""
        print(f"Scanning network {network_base}.{start}-{end} for ESSL devices...")
        found_devices = []
        
        for i in range(start, end + 1):
            ip = f"{network_base}.{i}"
            if self.test_device_connection(ip, verbose=False):
                found_devices.append(ip)
                print(f"✓ Found ESSL device at {ip}")
        
        return found_devices
    
    def test_device_connection(self, ip, port=None, verbose=True):
        """Test connection to a specific device"""
        if port is None:
            port = self.default_port
        
        if verbose:
            print(f"Testing connection to {ip}:{port}...")
        
        try:
            # Create UDP socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(self.timeout)
            
            # Create connection command
            command = self._create_command(1000, b'')  # CMD_CONNECT
            
            # Send command
            sock.sendto(command, (ip, port))
            
            # Wait for response
            data, addr = sock.recvfrom(1024)
            sock.close()
            
            if len(data) >= 8:
                reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
                if command_id == 2000:  # CMD_ACK_OK
                    if verbose:
                        print(f"✓ Successfully connected to device at {ip}:{port}")
                    return True
            
            if verbose:
                print(f"✗ Device at {ip}:{port} responded but connection failed")
            return False
            
        except socket.timeout:
            if verbose:
                print(f"✗ Connection timeout to {ip}:{port}")
            return False
        except Exception as e:
            if verbose:
                print(f"✗ Connection error to {ip}:{port}: {str(e)}")
            return False
    
    def get_device_info(self, ip, port=None):
        """Get detailed information from device"""
        if port is None:
            port = self.default_port
        
        print(f"Getting device information from {ip}:{port}...")
        
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.settimeout(self.timeout)
            
            # Connect first
            command = self._create_command(1000, b'')
            sock.sendto(command, (ip, port))
            data, addr = sock.recvfrom(1024)
            
            if len(data) >= 8:
                reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
                if command_id == 2000:
                    print(f"✓ Connected to device (Session ID: {session_id})")
                    
                    # Get device time as a test
                    time_command = self._create_command(11, b'', session_id)
                    sock.sendto(time_command, (ip, port))
                    
                    try:
                        time_data, addr = sock.recvfrom(1024)
                        print(f"✓ Device responded to time request")
                    except socket.timeout:
                        print("⚠ Device time request timed out")
                    
                    # Disconnect
                    disconnect_command = self._create_command(1001, b'', session_id)
                    sock.sendto(disconnect_command, (ip, port))
                    
                    sock.close()
                    return True
            
            sock.close()
            return False
            
        except Exception as e:
            print(f"✗ Error getting device info: {str(e)}")
            return False
    
    def _create_command(self, command_id, data, session_id=0):
        """Create command packet for device communication"""
        reply_id = 1
        checksum = sum(data) & 0xFFFF if data else 0
        header = struct.pack('<HHHH', reply_id, command_id, checksum, session_id)
        return header + data
    
    def create_env_file(self, device_ip):
        """Create .env file with device configuration"""
        env_content = f"""# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=sqlite:///staff_management.db

# ESSL X990+ID Biometric Device Configuration
ESSL_DEVICE_IP={device_ip}
ESSL_DEVICE_PORT=4370
ESSL_DEVICE_PASSWORD=0

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216
"""
        
        with open('.env', 'w') as f:
            f.write(env_content)
        
        print(f"✓ Created .env file with device IP: {device_ip}")

def main():
    """Main setup function"""
    print("=" * 60)
    print("ESSL X990+ID Device Setup Helper")
    print("=" * 60)
    
    helper = DeviceSetupHelper()
    
    print("\n1. Testing common device IPs...")
    common_ips = [
        "*************", "*************", "*************",
        "*************", "*************", "*************",
        "**********", "**********"
    ]
    
    found_devices = []
    for ip in common_ips:
        if helper.test_device_connection(ip):
            found_devices.append(ip)
    
    if not found_devices:
        print("\n2. Scanning local network for devices...")
        found_devices = helper.scan_network_for_devices("192.168.1", 1, 50)
        
        if not found_devices:
            found_devices = helper.scan_network_for_devices("192.168.0", 1, 50)
    
    if found_devices:
        print(f"\n✓ Found {len(found_devices)} ESSL device(s):")
        for i, device in enumerate(found_devices, 1):
            print(f"  {i}. {device}")
        
        # Test first device in detail
        print(f"\n3. Testing device {found_devices[0]} in detail...")
        helper.get_device_info(found_devices[0])
        
        # Create .env file
        print(f"\n4. Creating configuration...")
        helper.create_env_file(found_devices[0])
        
        print(f"\n✓ Setup completed successfully!")
        print(f"Device IP configured: {found_devices[0]}")
        print(f"Next steps:")
        print(f"  1. Verify device settings in device menu")
        print(f"  2. Run: python app.py")
        print(f"  3. Access admin panel and test device connection")
        
    else:
        print("\n✗ No ESSL devices found on the network.")
        print("\nTroubleshooting steps:")
        print("1. Check device power and network connection")
        print("2. Verify device IP address in device menu: Menu > Comm > Ethernet")
        print("3. Ensure device and computer are on same network")
        print("4. Check firewall settings")
        print("5. Try manual IP configuration:")
        
        manual_ip = input("\nEnter device IP manually (or press Enter to skip): ").strip()
        if manual_ip:
            if helper.test_device_connection(manual_ip):
                helper.create_env_file(manual_ip)
                print(f"✓ Configuration created with IP: {manual_ip}")
            else:
                print(f"✗ Could not connect to {manual_ip}")

if __name__ == '__main__':
    main()
