{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-fingerprint"></i> Fingerprints - {{ staff.full_name }}</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.register_fingerprint', staff_id=staff.id) }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Add Fingerprint
            </a>
            <a href="{{ url_for('admin.staff_detail', staff_id=staff.id) }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Staff
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-list"></i> Registered Fingerprints</h5>
            </div>
            <div class="card-body">
                {% if fingerprints %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Finger</th>
                                    <th>Device User ID</th>
                                    <th>Registration Date</th>
                                    <th>Status</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for fp in fingerprints %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-fingerprint text-primary me-2"></i>
                                            <div>
                                                <strong>
                                                    {% if fp.finger_id == 0 %}Right Thumb
                                                    {% elif fp.finger_id == 1 %}Right Index
                                                    {% elif fp.finger_id == 2 %}Right Middle
                                                    {% elif fp.finger_id == 3 %}Right Ring
                                                    {% elif fp.finger_id == 4 %}Right Little
                                                    {% elif fp.finger_id == 5 %}Left Thumb
                                                    {% elif fp.finger_id == 6 %}Left Index
                                                    {% elif fp.finger_id == 7 %}Left Middle
                                                    {% elif fp.finger_id == 8 %}Left Ring
                                                    {% elif fp.finger_id == 9 %}Left Little
                                                    {% else %}Unknown
                                                    {% endif %}
                                                </strong>
                                                <br><small class="text-muted">Finger ID: {{ fp.finger_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">{{ fp.device_user_id }}</span>
                                    </td>
                                    <td>
                                        {{ fp.created_at.strftime('%m/%d/%Y %H:%M') }}
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if fp.is_active else 'secondary' }}">
                                            {{ 'Active' if fp.is_active else 'Inactive' }}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" 
                                                    onclick="testFingerprint({{ fp.id }})" title="Test Fingerprint">
                                                <i class="fas fa-vial"></i>
                                            </button>
                                            {% if fp.is_active %}
                                                <button type="button" class="btn btn-sm btn-outline-warning" 
                                                        onclick="deactivateFingerprint({{ fp.id }})" title="Deactivate">
                                                    <i class="fas fa-pause"></i>
                                                </button>
                                            {% else %}
                                                <button type="button" class="btn btn-sm btn-outline-success" 
                                                        onclick="activateFingerprint({{ fp.id }})" title="Activate">
                                                    <i class="fas fa-play"></i>
                                                </button>
                                            {% endif %}
                                            <button type="button" class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteFingerprint({{ fp.id }})" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <div class="text-center py-5">
                        <i class="fas fa-fingerprint fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No Fingerprints Registered</h5>
                        <p class="text-muted">This staff member hasn't registered any fingerprints yet.</p>
                        <a href="{{ url_for('admin.register_fingerprint', staff_id=staff.id) }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Register First Fingerprint
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Fingerprint Management</h5>
            </div>
            <div class="card-body">
                <h6>Registration Guidelines:</h6>
                <ul class="small">
                    <li>Each staff member can register up to 10 fingerprints</li>
                    <li>Recommended: Register both index fingers</li>
                    <li>Use different fingers for backup access</li>
                    <li>Test fingerprints after registration</li>
                </ul>
                
                <hr>
                
                <h6>Fingerprint Status:</h6>
                <ul class="small">
                    <li><span class="badge bg-success">Active</span> - Can be used for attendance</li>
                    <li><span class="badge bg-secondary">Inactive</span> - Temporarily disabled</li>
                </ul>
                
                <hr>
                
                <div class="alert alert-warning small">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>Note:</strong> Deleting a fingerprint will permanently remove it from the device.
                </div>
            </div>
        </div>
        
        <!-- Quick Stats -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Statistics</h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <h3 class="text-primary">{{ fingerprints|length }}</h3>
                        <small class="text-muted">Total Registered</small>
                    </div>
                    <div class="col-6">
                        <h3 class="text-success">{{ fingerprints|selectattr('is_active')|list|length }}</h3>
                        <small class="text-muted">Active</small>
                    </div>
                </div>
                
                <hr>
                
                <div class="progress" style="height: 10px;">
                    {% set progress = (fingerprints|length / 10 * 100) %}
                    <div class="progress-bar" role="progressbar" style="width: {{ progress }}%" 
                         aria-valuenow="{{ progress }}" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
                <small class="text-muted">{{ fingerprints|length }}/10 fingerprints registered</small>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function testFingerprint(fingerprintId) {
    const btn = event.target.closest('button');
    const originalHtml = btn.innerHTML;
    
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;
    
    // Simulate fingerprint test
    setTimeout(() => {
        alert('Fingerprint test completed successfully!');
        btn.innerHTML = originalHtml;
        btn.disabled = false;
    }, 2000);
}

function activateFingerprint(fingerprintId) {
    if (confirm('Are you sure you want to activate this fingerprint?')) {
        // Implement activation logic
        alert('Fingerprint activated successfully!');
        location.reload();
    }
}

function deactivateFingerprint(fingerprintId) {
    if (confirm('Are you sure you want to deactivate this fingerprint?')) {
        // Implement deactivation logic
        alert('Fingerprint deactivated successfully!');
        location.reload();
    }
}

function deleteFingerprint(fingerprintId) {
    if (confirm('Are you sure you want to permanently delete this fingerprint?\n\nThis action cannot be undone and will remove the fingerprint from the biometric device.')) {
        // Implement deletion logic
        alert('Fingerprint deleted successfully!');
        location.reload();
    }
}
</script>
{% endblock %}
