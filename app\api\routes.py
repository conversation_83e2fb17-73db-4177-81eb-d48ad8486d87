from flask import request, jsonify, current_app
from datetime import datetime, date
from app import db
from app.api import bp
from app.models import Staff, Attendance, AttendanceLog, FingerprintTemplate

@bp.route('/attendance/push', methods=['POST'])
def push_attendance():
    """Receive attendance data from biometric device"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({'error': 'No data provided'}), 400
        
        # Expected data format:
        # {
        #     "device_user_id": 123,
        #     "timestamp": "2023-12-01 09:00:00",
        #     "punch_type": "in",  # in, out, break_start, break_end
        #     "device_id": "ESSL_001"
        # }
        
        required_fields = ['device_user_id', 'timestamp']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Parse timestamp
        try:
            timestamp = datetime.strptime(data['timestamp'], '%Y-%m-%d %H:%M:%S')
        except ValueError:
            return jsonify({'error': 'Invalid timestamp format. Use YYYY-MM-DD HH:MM:SS'}), 400
        
        # Create attendance log entry
        log_entry = AttendanceLog(
            device_user_id=data['device_user_id'],
            timestamp=timestamp,
            punch_type=data.get('punch_type', 'unknown'),
            device_id=data.get('device_id'),
            raw_data=str(data)
        )
        
        db.session.add(log_entry)
        db.session.commit()
        
        # Process the attendance
        process_attendance_log(log_entry)
        
        return jsonify({'message': 'Attendance data received successfully', 'log_id': log_entry.id}), 200
        
    except Exception as e:
        current_app.logger.error(f'Error processing attendance push: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@bp.route('/attendance/bulk-push', methods=['POST'])
def bulk_push_attendance():
    """Receive bulk attendance data from biometric device"""
    try:
        data = request.get_json()
        
        if not data or 'records' not in data:
            return jsonify({'error': 'No records provided'}), 400
        
        processed_count = 0
        error_count = 0
        
        for record in data['records']:
            try:
                # Parse timestamp
                timestamp = datetime.strptime(record['timestamp'], '%Y-%m-%d %H:%M:%S')
                
                # Create attendance log entry
                log_entry = AttendanceLog(
                    device_user_id=record['device_user_id'],
                    timestamp=timestamp,
                    punch_type=record.get('punch_type', 'unknown'),
                    device_id=record.get('device_id'),
                    raw_data=str(record)
                )
                
                db.session.add(log_entry)
                db.session.commit()
                
                # Process the attendance
                process_attendance_log(log_entry)
                processed_count += 1
                
            except Exception as e:
                current_app.logger.error(f'Error processing record: {str(e)}')
                error_count += 1
                continue
        
        return jsonify({
            'message': f'Processed {processed_count} records successfully',
            'processed': processed_count,
            'errors': error_count
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'Error processing bulk attendance push: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

def process_attendance_log(log_entry):
    """Process attendance log and update attendance records"""
    try:
        # Find staff by device user ID
        fingerprint = FingerprintTemplate.query.filter_by(
            device_user_id=log_entry.device_user_id,
            is_active=True
        ).first()
        
        if not fingerprint:
            current_app.logger.warning(f'No fingerprint found for device user ID: {log_entry.device_user_id}')
            return
        
        staff = fingerprint.staff
        attendance_date = log_entry.timestamp.date()
        
        # Get or create attendance record for the date
        attendance = Attendance.query.filter_by(
            staff_id=staff.id,
            date=attendance_date
        ).first()
        
        if not attendance:
            attendance = Attendance(
                staff_id=staff.id,
                date=attendance_date,
                status='present'
            )
            db.session.add(attendance)
        
        # Update attendance based on punch type
        if log_entry.punch_type == 'in' or (log_entry.punch_type == 'unknown' and not attendance.check_in_time):
            attendance.check_in_time = log_entry.timestamp
        elif log_entry.punch_type == 'out' or (log_entry.punch_type == 'unknown' and attendance.check_in_time and not attendance.check_out_time):
            attendance.check_out_time = log_entry.timestamp
        elif log_entry.punch_type == 'break_start':
            attendance.break_start_time = log_entry.timestamp
        elif log_entry.punch_type == 'break_end':
            attendance.break_end_time = log_entry.timestamp
        
        # Calculate total hours
        attendance.calculate_total_hours()
        
        # Mark log as processed
        log_entry.processed = True
        
        db.session.commit()
        
    except Exception as e:
        current_app.logger.error(f'Error processing attendance log {log_entry.id}: {str(e)}')
        db.session.rollback()

@bp.route('/staff/<staff_id>/attendance', methods=['GET'])
def get_staff_attendance(staff_id):
    """Get attendance data for a specific staff member"""
    try:
        staff = Staff.query.filter_by(staff_id=staff_id).first()
        if not staff:
            return jsonify({'error': 'Staff not found'}), 404
        
        # Get date range from query parameters
        start_date = request.args.get('start_date')
        end_date = request.args.get('end_date')
        
        query = Attendance.query.filter_by(staff_id=staff.id)
        
        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                query = query.filter(Attendance.date >= start_date)
            except ValueError:
                return jsonify({'error': 'Invalid start_date format. Use YYYY-MM-DD'}), 400
        
        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                query = query.filter(Attendance.date <= end_date)
            except ValueError:
                return jsonify({'error': 'Invalid end_date format. Use YYYY-MM-DD'}), 400
        
        attendance_records = query.order_by(Attendance.date.desc()).all()
        
        # Format response
        records = []
        for record in attendance_records:
            records.append({
                'date': record.date.isoformat(),
                'check_in_time': record.check_in_time.isoformat() if record.check_in_time else None,
                'check_out_time': record.check_out_time.isoformat() if record.check_out_time else None,
                'break_start_time': record.break_start_time.isoformat() if record.break_start_time else None,
                'break_end_time': record.break_end_time.isoformat() if record.break_end_time else None,
                'status': record.status,
                'total_hours': record.total_hours,
                'overtime_hours': record.overtime_hours,
                'is_manual_entry': record.is_manual_entry,
                'notes': record.notes
            })
        
        return jsonify({
            'staff_id': staff.staff_id,
            'staff_name': staff.full_name,
            'records': records
        }), 200
        
    except Exception as e:
        current_app.logger.error(f'Error getting staff attendance: {str(e)}')
        return jsonify({'error': 'Internal server error'}), 500

@bp.route('/device/status', methods=['GET'])
def device_status():
    """Check biometric device connection status"""
    try:
        from app.services.biometric_service import ESSLDevice

        device_ip = current_app.config.get('ESSL_DEVICE_IP')
        device_port = current_app.config.get('ESSL_DEVICE_PORT', 4370)
        device_password = current_app.config.get('ESSL_DEVICE_PASSWORD', 0)

        device = ESSLDevice(device_ip, device_port, device_password)
        connected = device.connect()

        if connected:
            device.disconnect()

        return jsonify({
            'connected': connected,
            'device_ip': device_ip,
            'device_port': device_port,
            'timestamp': datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        current_app.logger.error(f'Error checking device status: {str(e)}')
        return jsonify({
            'connected': False,
            'error': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 200

@bp.route('/device/test', methods=['POST'])
def test_device():
    """Test biometric device functionality"""
    try:
        from app.services.biometric_service import ESSLDevice

        device_ip = current_app.config.get('ESSL_DEVICE_IP')
        device_port = current_app.config.get('ESSL_DEVICE_PORT', 4370)
        device_password = current_app.config.get('ESSL_DEVICE_PASSWORD', 0)

        device = ESSLDevice(device_ip, device_port, device_password)

        if device.connect():
            # Test basic communication
            device.disconnect()
            return jsonify({
                'success': True,
                'message': 'Device communication test successful',
                'timestamp': datetime.utcnow().isoformat()
            }), 200
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to connect to device',
                'timestamp': datetime.utcnow().isoformat()
            }), 200

    except Exception as e:
        current_app.logger.error(f'Error testing device: {str(e)}')
        return jsonify({
            'success': False,
            'message': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 200

@bp.route('/device/info', methods=['GET'])
def get_device_info():
    """Get detailed device information"""
    try:
        from app.services.biometric_service import ESSLDevice

        device_ip = current_app.config.get('ESSL_DEVICE_IP')
        device_port = current_app.config.get('ESSL_DEVICE_PORT', 4370)
        device_password = current_app.config.get('ESSL_DEVICE_PASSWORD', 0)

        device = ESSLDevice(device_ip, device_port, device_password)

        if device.connect():
            device_info = device.get_device_info()
            device.disconnect()

            if device_info:
                return jsonify({
                    'success': True,
                    'device_info': device_info,
                    'timestamp': datetime.utcnow().isoformat()
                }), 200
            else:
                return jsonify({
                    'success': False,
                    'message': 'Failed to retrieve device information',
                    'timestamp': datetime.utcnow().isoformat()
                }), 200
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to connect to device',
                'timestamp': datetime.utcnow().isoformat()
            }), 200

    except Exception as e:
        current_app.logger.error(f'Error getting device info: {str(e)}')
        return jsonify({
            'success': False,
            'message': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500

@bp.route('/sync-attendance', methods=['POST'])
def sync_attendance():
    """Manually trigger attendance sync from device"""
    try:
        from app.services.biometric_service import BiometricService

        count = BiometricService.sync_attendance_from_device()

        return jsonify({
            'success': True,
            'message': f'Successfully synced {count} attendance records',
            'count': count,
            'timestamp': datetime.utcnow().isoformat()
        }), 200

    except Exception as e:
        current_app.logger.error(f'Error syncing attendance: {str(e)}')
        return jsonify({
            'success': False,
            'message': str(e),
            'timestamp': datetime.utcnow().isoformat()
        }), 500

@bp.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.utcnow().isoformat(),
        'version': '1.0.0'
    }), 200
