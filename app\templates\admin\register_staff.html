{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-user-plus"></i> Register New Staff</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.staff_list') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Staff List
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-user"></i> Staff Information</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <!-- Personal Information Section -->
                    <h6 class="text-primary mb-3"><i class="fas fa-id-card"></i> Personal Information</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.staff_id.label(class="form-label") }}
                            {{ form.staff_id(class="form-control") }}
                            {% if form.staff_id.errors %}
                                <div class="text-danger">
                                    {% for error in form.staff_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.email.label(class="form-label") }}
                            {{ form.email(class="form-control") }}
                            {% if form.email.errors %}
                                <div class="text-danger">
                                    {% for error in form.email.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.first_name.label(class="form-label") }}
                            {{ form.first_name(class="form-control") }}
                            {% if form.first_name.errors %}
                                <div class="text-danger">
                                    {% for error in form.first_name.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.last_name.label(class="form-label") }}
                            {{ form.last_name(class="form-control") }}
                            {% if form.last_name.errors %}
                                <div class="text-danger">
                                    {% for error in form.last_name.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.phone.label(class="form-label") }}
                            {{ form.phone(class="form-control") }}
                            {% if form.phone.errors %}
                                <div class="text-danger">
                                    {% for error in form.phone.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.date_of_birth.label(class="form-label") }}
                            {{ form.date_of_birth(class="form-control") }}
                            {% if form.date_of_birth.errors %}
                                <div class="text-danger">
                                    {% for error in form.date_of_birth.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.gender.label(class="form-label") }}
                            {{ form.gender(class="form-select") }}
                            {% if form.gender.errors %}
                                <div class="text-danger">
                                    {% for error in form.gender.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.address.label(class="form-label") }}
                        {{ form.address(class="form-control", rows="3") }}
                        {% if form.address.errors %}
                            <div class="text-danger">
                                {% for error in form.address.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <hr>
                    
                    <!-- Employment Information Section -->
                    <h6 class="text-primary mb-3"><i class="fas fa-briefcase"></i> Employment Information</h6>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.department.label(class="form-label") }}
                            {{ form.department(class="form-control") }}
                            {% if form.department.errors %}
                                <div class="text-danger">
                                    {% for error in form.department.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.designation.label(class="form-label") }}
                            {{ form.designation(class="form-control") }}
                            {% if form.designation.errors %}
                                <div class="text-danger">
                                    {% for error in form.designation.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.date_of_joining.label(class="form-label") }}
                            {{ form.date_of_joining(class="form-control") }}
                            {% if form.date_of_joining.errors %}
                                <div class="text-danger">
                                    {% for error in form.date_of_joining.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.employee_type.label(class="form-label") }}
                            {{ form.employee_type(class="form-select") }}
                            {% if form.employee_type.errors %}
                                <div class="text-danger">
                                    {% for error in form.employee_type.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.salary.label(class="form-label") }}
                            {{ form.salary(class="form-control") }}
                            {% if form.salary.errors %}
                                <div class="text-danger">
                                    {% for error in form.salary.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="form-check mt-4">
                                {{ form.is_active(class="form-check-input") }}
                                {{ form.is_active.label(class="form-check-label") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.staff_list') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Instructions</h5>
            </div>
            <div class="card-body">
                <h6>Staff Registration Process:</h6>
                <ol class="small">
                    <li>Fill in all required personal information</li>
                    <li>Enter employment details</li>
                    <li>Click "Register Staff" to save</li>
                    <li>After registration, you can set up fingerprint authentication</li>
                </ol>
                
                <hr>
                
                <h6>Important Notes:</h6>
                <ul class="small">
                    <li>Staff ID must be unique</li>
                    <li>Email address will be used for notifications</li>
                    <li>Date of joining cannot be in the future</li>
                    <li>Salary field is optional</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
