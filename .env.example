# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=mysql+pymysql://username:password@localhost/staff_management
# Alternative PostgreSQL: postgresql://username:password@localhost/staff_management

# ESSL X990+ID Biometric Device Configuration
# Configure your ESSL X990+ID device network settings

# Primary Device (main device for fingerprint registration)
ESSL_DEVICE_IP=*************        # Primary device IP address
ESSL_DEVICE_PORT=4370               # Default communication port (usually 4370)
ESSL_DEVICE_PASSWORD=0              # Device communication password (default: 0)

# Multiple Device Support
# List all device IPs separated by commas for multi-device setup
ESSL_DEVICE_LIST=*************,*************,*************
ESSL_DEVICE_COUNT=3                 # Number of devices (auto-calculated)

# Auto-Detection Settings
AUTO_DETECT_DEVICES=true            # Enable automatic device detection
DEVICE_SCAN_INTERVAL=300            # Scan for devices every 5 minutes (seconds)
DEVICE_TIMEOUT=5                    # Connection timeout in seconds

# Device Connection Notes:
# 1. Run 'python device_setup.py' for automatic device detection
# 2. Ensure all devices are connected to same network
# 3. Check device IPs in device menu: Menu > Comm > Ethernet
# 4. Test connections using ping: ping *************
# 5. Verify ports are not blocked by firewall
# 6. Default device password is usually 0 or empty
# 7. For multiple devices, ensure each has unique IP address

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216  # 16MB
