# Flask Configuration
FLASK_APP=app.py
FLASK_ENV=development
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=mysql+pymysql://username:password@localhost/staff_management
# Alternative PostgreSQL: postgresql://username:password@localhost/staff_management

# ESSL X990+ID Biometric Device Configuration
# Configure your ESSL X990+ID device network settings
ESSL_DEVICE_IP=*************        # Device IP address (check device network settings)
ESSL_DEVICE_PORT=4370               # Default communication port (usually 4370)
ESSL_DEVICE_PASSWORD=0              # Device communication password (default: 0)

# Device Connection Notes:
# 1. Ensure device is connected to same network
# 2. Check device IP in device menu: Menu > Comm > Ethernet
# 3. Test connection using ping command: ping *************
# 4. Verify port is not blocked by firewall
# 5. Default device password is usually 0 or empty

# Email Configuration (for notifications)
MAIL_SERVER=smtp.gmail.com
MAIL_PORT=587
MAIL_USE_TLS=True
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password

# Admin Configuration
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123

# File Upload Configuration
UPLOAD_FOLDER=uploads
MAX_CONTENT_LENGTH=16777216  # 16MB
