{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-tachometer-alt"></i> Welcome, {{ staff.first_name }}!
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('staff.request_leave') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-calendar-plus"></i> Request Leave
            </a>
        </div>
    </div>
</div>

<!-- Today's Status -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-day"></i> Today's Status - {{ moment().format('MMMM DD, YYYY') }}
                </h5>
            </div>
            <div class="card-body">
                {% if today_attendance %}
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="fas fa-sign-in-alt fa-2x text-success mb-2"></i>
                                <h6>Check In</h6>
                                <p class="h5 text-success">
                                    {{ today_attendance.check_in_time.strftime('%H:%M') if today_attendance.check_in_time else 'Not recorded' }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="fas fa-sign-out-alt fa-2x text-warning mb-2"></i>
                                <h6>Check Out</h6>
                                <p class="h5 text-warning">
                                    {{ today_attendance.check_out_time.strftime('%H:%M') if today_attendance.check_out_time else 'Not recorded' }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="fas fa-clock fa-2x text-info mb-2"></i>
                                <h6>Total Hours</h6>
                                <p class="h5 text-info">
                                    {{ "%.2f"|format(today_attendance.total_hours) if today_attendance.total_hours else '0.00' }}
                                </p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="fas fa-user-check fa-2x text-primary mb-2"></i>
                                <h6>Status</h6>
                                <p class="h5">
                                    <span class="badge bg-{{ 'success' if today_attendance.status == 'present' else 'warning' if today_attendance.status == 'late' else 'danger' }}">
                                        {{ today_attendance.status.title() }}
                                    </span>
                                </p>
                            </div>
                        </div>
                    </div>
                {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-calendar-times fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">No attendance recorded for today</h5>
                        <p class="text-muted">Please use the biometric device to mark your attendance.</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Monthly Statistics -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Days
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_days }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2 card-stats success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Present Days
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ present_days }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2 card-stats warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Absent Days
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ absent_days }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2 card-stats danger">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Late Days
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ late_days }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Recent Attendance -->
    <div class="col-lg-8 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-calendar-check"></i> Recent Attendance
                </h6>
                <a href="{{ url_for('staff.attendance_history') }}" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                {% if monthly_attendance %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Check In</th>
                                    <th>Check Out</th>
                                    <th>Hours</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for attendance in monthly_attendance[:10] %}
                                <tr>
                                    <td>{{ attendance.date.strftime('%m/%d/%Y') }}</td>
                                    <td>{{ attendance.check_in_time.strftime('%H:%M') if attendance.check_in_time else '-' }}</td>
                                    <td>{{ attendance.check_out_time.strftime('%H:%M') if attendance.check_out_time else '-' }}</td>
                                    <td>{{ "%.2f"|format(attendance.total_hours) if attendance.total_hours else '0.00' }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if attendance.status == 'present' else 'warning' if attendance.status == 'late' else 'danger' }}">
                                            {{ attendance.status.title() }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No attendance records found for this month.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Recent Leave Requests -->
    <div class="col-lg-4 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-calendar-times"></i> Recent Leaves
                </h6>
                <a href="{{ url_for('staff.leave_history') }}" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_leaves %}
                    {% for leave in recent_leaves %}
                    <div class="mb-3 p-3 border rounded">
                        <div class="d-flex justify-content-between align-items-start">
                            <div>
                                <h6 class="mb-1">{{ leave.leave_type.title() }} Leave</h6>
                                <p class="mb-1 text-muted small">
                                    {{ leave.start_date.strftime('%m/%d/%Y') }} - {{ leave.end_date.strftime('%m/%d/%Y') }}
                                </p>
                                <p class="mb-0 small">{{ leave.total_days }} day(s)</p>
                            </div>
                            <span class="badge bg-{{ 'warning' if leave.status == 'pending' else 'success' if leave.status == 'approved' else 'danger' }}">
                                {{ leave.status.title() }}
                            </span>
                        </div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">No leave requests found.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('staff.attendance_history') }}" class="btn btn-primary btn-block">
                            <i class="fas fa-calendar-check"></i><br>
                            View Attendance
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('staff.request_leave') }}" class="btn btn-success btn-block">
                            <i class="fas fa-calendar-plus"></i><br>
                            Request Leave
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('staff.profile') }}" class="btn btn-info btn-block">
                            <i class="fas fa-user"></i><br>
                            My Profile
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('auth.change_password') }}" class="btn btn-warning btn-block">
                            <i class="fas fa-key"></i><br>
                            Change Password
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>
{% endblock %}
