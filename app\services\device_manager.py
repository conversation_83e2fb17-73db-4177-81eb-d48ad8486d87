"""
Multi-Device Manager for ESSL Biometric Devices
Handles automatic detection and management of multiple biometric devices
"""

import threading
import time
import socket
import struct
from datetime import datetime
from flask import current_app
from app import db
from app.models import AttendanceLog, FingerprintTemplate
from app.services.biometric_service import ESSLDevice

class DeviceManager:
    """Manages multiple biometric devices"""
    
    def __init__(self):
        self.devices = {}
        self.active_devices = {}
        self.scan_thread = None
        self.running = False
        self.last_scan = None
        
    def start(self):
        """Start the device manager"""
        self.running = True
        self.scan_thread = threading.Thread(target=self._scan_loop, daemon=True)
        self.scan_thread.start()
        current_app.logger.info("Device Manager started")
    
    def stop(self):
        """Stop the device manager"""
        self.running = False
        if self.scan_thread:
            self.scan_thread.join(timeout=5)
        current_app.logger.info("Device Manager stopped")
    
    def _scan_loop(self):
        """Main scanning loop"""
        while self.running:
            try:
                self.scan_for_devices()
                scan_interval = current_app.config.get('DEVICE_SCAN_INTERVAL', 300)
                time.sleep(scan_interval)
            except Exception as e:
                current_app.logger.error(f"Error in device scan loop: {str(e)}")
                time.sleep(60)  # Wait 1 minute before retrying
    
    def scan_for_devices(self):
        """Scan network for available devices"""
        current_app.logger.info("Scanning for biometric devices...")
        
        # Get configured device list
        device_list = current_app.config.get('ESSL_DEVICE_LIST', '')
        if device_list:
            device_ips = [ip.strip() for ip in device_list.split(',')]
        else:
            # Fallback to primary device
            primary_ip = current_app.config.get('ESSL_DEVICE_IP')
            device_ips = [primary_ip] if primary_ip else []
        
        # Test each configured device
        for ip in device_ips:
            if ip:
                self._test_and_register_device(ip)
        
        # Auto-detect new devices if enabled
        if current_app.config.get('AUTO_DETECT_DEVICES', 'false').lower() == 'true':
            self._auto_detect_devices()
        
        self.last_scan = datetime.utcnow()
        current_app.logger.info(f"Device scan completed. Active devices: {len(self.active_devices)}")
    
    def _test_and_register_device(self, ip):
        """Test and register a specific device"""
        port = current_app.config.get('ESSL_DEVICE_PORT', 4370)
        timeout = current_app.config.get('DEVICE_TIMEOUT', 5)
        
        device_key = f"{ip}:{port}"
        
        try:
            device = ESSLDevice(ip, port, 0)
            device.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            device.socket.settimeout(timeout)
            
            if device.connect():
                device_info = {
                    'ip': ip,
                    'port': port,
                    'status': 'connected',
                    'last_seen': datetime.utcnow(),
                    'model': 'ESSL X990+ID',
                    'session_id': device.session_id
                }
                
                self.devices[device_key] = device_info
                self.active_devices[device_key] = device
                
                current_app.logger.info(f"Device {ip}:{port} is active")
                device.disconnect()
                return True
            else:
                # Mark as inactive if previously active
                if device_key in self.active_devices:
                    del self.active_devices[device_key]
                    current_app.logger.warning(f"Device {ip}:{port} became inactive")
                return False
                
        except Exception as e:
            if device_key in self.active_devices:
                del self.active_devices[device_key]
            current_app.logger.error(f"Error testing device {ip}:{port}: {str(e)}")
            return False
    
    def _auto_detect_devices(self):
        """Auto-detect new devices on the network"""
        # Get local network ranges
        networks = self._get_local_networks()
        
        for network in networks[:2]:  # Limit to first 2 networks to avoid long scans
            self._scan_network_range(network)
    
    def _get_local_networks(self):
        """Get local network ranges for scanning"""
        try:
            import netifaces
            networks = []
            
            for interface in netifaces.interfaces():
                addrs = netifaces.ifaddresses(interface)
                if netifaces.AF_INET in addrs:
                    for addr_info in addrs[netifaces.AF_INET]:
                        ip = addr_info.get('addr')
                        if ip and not ip.startswith('127.'):
                            network_base = '.'.join(ip.split('.')[:-1])
                            if network_base not in networks:
                                networks.append(network_base)
            
            return networks
        except ImportError:
            # Fallback networks
            return ['192.168.1', '192.168.0', '10.0.0']
    
    def _scan_network_range(self, network_base, start=1, end=254):
        """Scan a network range for devices"""
        port = current_app.config.get('ESSL_DEVICE_PORT', 4370)
        
        # Use threading for faster scanning
        threads = []
        max_threads = 20
        
        def test_ip(ip):
            device_key = f"{ip}:{port}"
            if device_key not in self.devices:  # Only test unknown devices
                if self._test_and_register_device(ip):
                    current_app.logger.info(f"Auto-detected new device: {ip}")
        
        for i in range(start, min(start + 50, end + 1)):  # Limit scan range
            ip = f"{network_base}.{i}"
            thread = threading.Thread(target=test_ip, args=(ip,))
            threads.append(thread)
            thread.start()
            
            if len(threads) >= max_threads:
                for t in threads:
                    t.join()
                threads = []
        
        # Wait for remaining threads
        for thread in threads:
            thread.join()
    
    def get_active_devices(self):
        """Get list of active devices"""
        return list(self.active_devices.keys())
    
    def get_device_info(self, device_key=None):
        """Get device information"""
        if device_key:
            return self.devices.get(device_key)
        return self.devices
    
    def sync_attendance_from_all_devices(self):
        """Sync attendance data from all active devices"""
        total_synced = 0
        
        for device_key, device in self.active_devices.items():
            try:
                if device.connect():
                    logs = device.get_attendance_logs()
                    
                    for log_data in logs:
                        # Check if log already exists
                        existing_log = AttendanceLog.query.filter_by(
                            device_user_id=log_data['device_user_id'],
                            timestamp=log_data['timestamp']
                        ).first()
                        
                        if not existing_log:
                            log_entry = AttendanceLog(
                                device_user_id=log_data['device_user_id'],
                                timestamp=log_data['timestamp'],
                                punch_type=log_data['punch_type'],
                                device_id=device_key,
                                raw_data=str(log_data)
                            )
                            
                            db.session.add(log_entry)
                            total_synced += 1
                    
                    device.disconnect()
                    
            except Exception as e:
                current_app.logger.error(f"Error syncing from device {device_key}: {str(e)}")
        
        if total_synced > 0:
            db.session.commit()
        
        return total_synced
    
    def get_device_status_summary(self):
        """Get summary of all device statuses"""
        return {
            'total_devices': len(self.devices),
            'active_devices': len(self.active_devices),
            'last_scan': self.last_scan.isoformat() if self.last_scan else None,
            'devices': self.devices
        }

# Global device manager instance
device_manager = DeviceManager()

def init_device_manager(app):
    """Initialize device manager with Flask app"""
    with app.app_context():
        device_manager.start()

def get_device_manager():
    """Get the global device manager instance"""
    return device_manager
