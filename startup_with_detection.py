#!/usr/bin/env python3
"""
Staff Management System with Automatic Device Detection
Automatically detects biometric devices and starts the application
"""

import os
import sys
import time
from datetime import datetime

def print_banner():
    """Print startup banner"""
    print("=" * 70)
    print("🚀 Staff Management System with Auto-Detection")
    print("=" * 70)
    print(f"⏰ Starting at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def check_dependencies():
    """Check if required dependencies are installed"""
    print("📦 Checking dependencies...")
    
    required_packages = [
        'flask', 'flask_sqlalchemy', 'flask_login', 
        'flask_wtf', 'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"   ❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    print("✅ All dependencies satisfied")
    return True

def run_device_detection():
    """Run automatic device detection"""
    print("\n🔍 Running automatic device detection...")
    
    try:
        # Import the auto-detector
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from app.services.auto_detector import BiometricAutoDetector
        
        detector = BiometricAutoDetector()
        
        # Run detection
        success = detector.auto_detect_and_configure()
        
        if success:
            devices = detector.get_detected_devices()
            primary = detector.get_primary_device()
            
            print(f"🎉 Auto-detection successful!")
            print(f"   📱 Found {len(devices)} device(s)")
            print(f"   🎯 Primary device: {primary['ip']}:{primary['port']}")
            
            for i, device in enumerate(devices, 1):
                print(f"   Device {i}: {device['ip']}:{device['port']} - {device['model']}")
            
            return True
        else:
            print("⚠️ No devices found during auto-detection")
            print("   You can manually configure devices later in the admin panel")
            return False
            
    except Exception as e:
        print(f"❌ Error during device detection: {str(e)}")
        print("   Continuing without auto-detection...")
        return False

def initialize_database():
    """Initialize the database"""
    print("\n💾 Initializing database...")
    
    try:
        from app import create_app, db
        from app.models import User, Staff, Department
        
        app = create_app()
        
        with app.app_context():
            # Create tables
            db.create_all()
            
            # Check if admin user exists
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                print("   👤 Creating admin user...")
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    is_admin=True
                )
                admin_user.set_password('admin123')
                db.session.add(admin_user)
                db.session.commit()
                print("   ✅ Admin user created (admin/admin123)")
            else:
                print("   ✅ Admin user already exists")
            
            # Check if default department exists
            default_dept = Department.query.filter_by(name='General').first()
            if not default_dept:
                print("   🏢 Creating default department...")
                default_dept = Department(
                    name='General',
                    description='Default department for staff'
                )
                db.session.add(default_dept)
                db.session.commit()
                print("   ✅ Default department created")
            else:
                print("   ✅ Default department exists")
        
        print("✅ Database initialized successfully")
        return True
        
    except Exception as e:
        print(f"❌ Database initialization error: {str(e)}")
        return False

def start_application():
    """Start the Flask application"""
    print("\n🌐 Starting Flask application...")
    
    try:
        from app import create_app
        
        app = create_app()
        
        print("✅ Application created successfully")
        print("\n🚀 Starting server...")
        print("   📍 URL: http://localhost:5000")
        print("   👤 Admin login: admin / admin123")
        print("   🛑 Press Ctrl+C to stop")
        print("\n" + "=" * 50)
        
        # Start the Flask development server
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=False  # Disable reloader to prevent double initialization
        )
        
    except KeyboardInterrupt:
        print("\n\n🛑 Application stopped by user")
    except Exception as e:
        print(f"\n❌ Application error: {str(e)}")

def main():
    """Main startup function"""
    print_banner()
    
    # Step 1: Check dependencies
    if not check_dependencies():
        print("\n❌ Cannot start application due to missing dependencies")
        sys.exit(1)
    
    # Step 2: Run device detection
    print("\n" + "=" * 50)
    device_detection_success = run_device_detection()
    
    # Step 3: Initialize database
    print("\n" + "=" * 50)
    if not initialize_database():
        print("\n❌ Cannot start application due to database errors")
        sys.exit(1)
    
    # Step 4: Start application
    print("\n" + "=" * 50)
    if device_detection_success:
        print("🎉 System ready with biometric devices configured!")
    else:
        print("⚠️ System ready but no biometric devices found")
        print("   Use Admin → Device Management → Auto-Detect to find devices later")
    
    print("\n📋 Next steps after startup:")
    print("   1. Open http://localhost:5000 in your browser")
    print("   2. Login with admin/admin123")
    print("   3. Go to Admin → Device Management to verify device connections")
    print("   4. Register staff and their fingerprints")
    print("   5. Start using the attendance system")
    
    # Small delay to let user read the messages
    time.sleep(3)
    
    start_application()

if __name__ == '__main__':
    main()
