#!/usr/bin/env python3
"""
Local Biometric Device Scanner
Scans for biometric devices connected directly to the system
"""

import sys
import os
from datetime import datetime

def print_banner():
    """Print scanner banner"""
    print("=" * 70)
    print("🔍 Local Biometric Device Scanner")
    print("=" * 70)
    print(f"⏰ Scan started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

def scan_usb_devices():
    """Scan for USB-connected biometric devices"""
    print("📱 Scanning USB devices...")
    usb_devices = []
    
    try:
        import usb.core
        import usb.util
        
        print("   🔍 Enumerating USB devices...")
        
        # Common vendor IDs for biometric devices
        biometric_vendors = {
            0x2808: "ESSL",
            0x1234: "Generic Biometric",
            0x0483: "STMicroelectronics",
            0x04e8: "Samsung",
            0x27c6: "Goodix",
            0x1a86: "QinHeng Electronics",
            0x0403: "FTDI",
        }
        
        devices = usb.core.find(find_all=True)
        device_count = 0
        
        for device in devices:
            device_count += 1
            try:
                vendor_name = biometric_vendors.get(device.idVendor, "Unknown")
                
                # Check if it's a known biometric vendor
                if device.idVendor in biometric_vendors:
                    print(f"   ✅ BIOMETRIC DEVICE FOUND!")
                    print(f"      Vendor: {vendor_name} (VID: {device.idVendor:04x})")
                    print(f"      Product ID: {device.idProduct:04x}")
                    
                    # Try to get device description
                    try:
                        product_name = usb.util.get_string(device, device.iProduct)
                        print(f"      Product: {product_name}")
                    except:
                        print(f"      Product: Unknown")
                    
                    try:
                        manufacturer = usb.util.get_string(device, device.iManufacturer)
                        print(f"      Manufacturer: {manufacturer}")
                    except:
                        print(f"      Manufacturer: Unknown")
                    
                    device_info = {
                        'type': 'usb',
                        'vendor_id': device.idVendor,
                        'product_id': device.idProduct,
                        'vendor_name': vendor_name,
                        'connection': f'USB (VID:{device.idVendor:04x}, PID:{device.idProduct:04x})'
                    }
                    usb_devices.append(device_info)
                    print()
                
                # Also check for devices with biometric-related product names
                else:
                    try:
                        product_name = usb.util.get_string(device, device.iProduct) or ""
                        if any(keyword in product_name.lower() for keyword in 
                               ['fingerprint', 'biometric', 'scanner', 'reader', 'attendance']):
                            print(f"   ✅ POTENTIAL BIOMETRIC DEVICE!")
                            print(f"      Product: {product_name}")
                            print(f"      Vendor ID: {device.idVendor:04x}")
                            print(f"      Product ID: {device.idProduct:04x}")
                            
                            device_info = {
                                'type': 'usb',
                                'vendor_id': device.idVendor,
                                'product_id': device.idProduct,
                                'vendor_name': 'Unknown',
                                'product_name': product_name,
                                'connection': f'USB (VID:{device.idVendor:04x}, PID:{device.idProduct:04x})'
                            }
                            usb_devices.append(device_info)
                            print()
                    except:
                        pass
                        
            except Exception as e:
                continue
        
        print(f"   📊 Total USB devices scanned: {device_count}")
        
    except ImportError:
        print("   ⚠️ pyusb not installed")
        print("   Install with: pip install pyusb")
        print("   Note: On Windows, you may also need libusb drivers")
    except Exception as e:
        print(f"   ❌ USB scan error: {str(e)}")
    
    return usb_devices

def scan_serial_devices():
    """Scan for Serial/COM port devices"""
    print("🔌 Scanning Serial/COM ports...")
    serial_devices = []
    
    try:
        import serial.tools.list_ports
        
        print("   🔍 Enumerating COM ports...")
        
        ports = serial.tools.list_ports.comports()
        
        if not ports:
            print("   ℹ️ No COM ports found")
            return serial_devices
        
        for port in ports:
            print(f"   📍 Port: {port.device}")
            print(f"      Description: {port.description}")
            print(f"      Hardware ID: {port.hwid}")
            
            # Check if it might be a biometric device
            description = (port.description or '').lower()
            hwid = (port.hwid or '').lower()
            
            biometric_keywords = [
                'biometric', 'fingerprint', 'essl', 'attendance', 
                'scanner', 'reader', 'sensor', 'zktech', 'zk'
            ]
            
            is_biometric = False
            
            # Check description
            for keyword in biometric_keywords:
                if keyword in description or keyword in hwid:
                    is_biometric = True
                    break
            
            # Check for specific vendor IDs
            biometric_vid_patterns = ['VID_2808', 'VID_1234', 'VID_0483']
            for pattern in biometric_vid_patterns:
                if pattern in hwid:
                    is_biometric = True
                    break
            
            if is_biometric:
                print(f"      ✅ POTENTIAL BIOMETRIC DEVICE!")
                device_info = {
                    'type': 'serial',
                    'port': port.device,
                    'description': port.description,
                    'hwid': port.hwid,
                    'connection': f'Serial ({port.device})'
                }
                serial_devices.append(device_info)
            
            print()
        
    except ImportError:
        print("   ⚠️ pyserial not installed")
        print("   Install with: pip install pyserial")
    except Exception as e:
        print(f"   ❌ Serial scan error: {str(e)}")
    
    return serial_devices

def scan_localhost_services():
    """Scan localhost for biometric services"""
    print("🌐 Scanning localhost services...")
    localhost_devices = []
    
    import socket
    
    # Common ports used by biometric devices
    test_ports = [4370, 8080, 9999, 8000, 5000, 3000, 8888]
    
    for port in test_ports:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(1)
            result = sock.connect_ex(('127.0.0.1', port))
            sock.close()
            
            if result == 0:
                print(f"   ✅ Service found on localhost:{port}")
                device_info = {
                    'type': 'localhost',
                    'port': port,
                    'connection': f'Localhost (127.0.0.1:{port})'
                }
                localhost_devices.append(device_info)
        except:
            pass
    
    if not localhost_devices:
        print("   ℹ️ No localhost services found on common biometric ports")
    
    return localhost_devices

def main():
    """Main scanning function"""
    print_banner()
    
    all_devices = []
    
    # Scan USB devices
    usb_devices = scan_usb_devices()
    all_devices.extend(usb_devices)
    
    print()
    
    # Scan Serial devices
    serial_devices = scan_serial_devices()
    all_devices.extend(serial_devices)
    
    print()
    
    # Scan localhost services
    localhost_devices = scan_localhost_services()
    all_devices.extend(localhost_devices)
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 SCAN SUMMARY")
    print("=" * 70)
    
    if all_devices:
        print(f"🎉 Found {len(all_devices)} potential biometric device(s):")
        print()
        
        for i, device in enumerate(all_devices, 1):
            print(f"Device {i}: {device['connection']}")
            if device['type'] == 'usb':
                print(f"   Type: USB Biometric Device")
                if 'vendor_name' in device:
                    print(f"   Vendor: {device['vendor_name']}")
                if 'product_name' in device:
                    print(f"   Product: {device['product_name']}")
            elif device['type'] == 'serial':
                print(f"   Type: Serial/COM Port Device")
                print(f"   Description: {device['description']}")
            elif device['type'] == 'localhost':
                print(f"   Type: Localhost Service")
            print()
        
        print("🔧 Next steps:")
        print("1. If USB devices found, ensure proper drivers are installed")
        print("2. For ESSL devices, check device network settings")
        print("3. Run the main application: python startup_with_detection.py")
        print("4. Use Admin → Device Management → Auto-Detect")
        
    else:
        print("❌ No biometric devices found")
        print()
        print("🔧 Troubleshooting:")
        print("1. Ensure biometric device is connected and powered on")
        print("2. Install USB drivers if using USB connection")
        print("3. Check device manager for unrecognized devices")
        print("4. For network devices, ensure they're on the same network")
        print("5. Try connecting device via different USB port")
        print()
        print("📦 Install additional libraries:")
        print("   pip install pyusb pyserial")

if __name__ == '__main__':
    main()
