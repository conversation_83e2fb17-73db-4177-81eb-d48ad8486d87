{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-download"></i> Export
            </button>
        </div>
    </div>
</div>

<!-- Device Status Alert -->
<div class="row mb-3">
    <div class="col-12">
        <div class="alert alert-info" id="device-status-alert">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-wifi me-2"></i>
                    <strong>Biometric Device Status:</strong>
                    <span id="device-status-text">Checking connection...</span>
                </div>
                <div>
                    <button type="button" class="btn btn-sm btn-outline-primary" onclick="checkDeviceStatus()">
                        <i class="fas fa-sync-alt"></i> Refresh
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-success" onclick="testDevice()">
                        <i class="fas fa-vial"></i> Test Device
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-primary shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Staff
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ total_staff }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-success shadow h-100 py-2 card-stats success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Present Today
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ present_today }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-warning shadow h-100 py-2 card-stats warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Absent Today
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ absent_today }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-left-danger shadow h-100 py-2 card-stats danger">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Pending Leaves
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">{{ pending_leaves }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-times fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Content Row -->
<div class="row">
    <!-- Recent Attendance Logs -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-clock"></i> Recent Attendance Logs
                </h6>
                <a href="{{ url_for('admin.attendance_list') }}" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                {% if recent_logs %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>User ID</th>
                                    <th>Time</th>
                                    <th>Type</th>
                                    <th>Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in recent_logs %}
                                <tr>
                                    <td>{{ log.device_user_id }}</td>
                                    <td>{{ log.timestamp.strftime('%H:%M') }}</td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if log.punch_type == 'in' else 'warning' }}">
                                            {{ log.punch_type.title() }}
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'success' if log.processed else 'secondary' }}">
                                            {{ 'Processed' if log.processed else 'Pending' }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No recent attendance logs.</p>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Pending Leave Requests -->
    <div class="col-lg-6 mb-4">
        <div class="card shadow">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-calendar-times"></i> Pending Leave Requests
                </h6>
                <a href="#" class="btn btn-sm btn-primary">View All</a>
            </div>
            <div class="card-body">
                {% if pending_leave_requests %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Staff</th>
                                    <th>Type</th>
                                    <th>Dates</th>
                                    <th>Days</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for leave in pending_leave_requests %}
                                <tr>
                                    <td>{{ leave.staff.full_name }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ leave.leave_type.title() }}</span>
                                    </td>
                                    <td>{{ leave.start_date.strftime('%m/%d') }} - {{ leave.end_date.strftime('%m/%d') }}</td>
                                    <td>{{ leave.total_days }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-success" title="Approve">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" title="Reject">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                {% else %}
                    <p class="text-muted">No pending leave requests.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">
                    <i class="fas fa-bolt"></i> Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.register_staff') }}" class="btn btn-primary btn-block">
                            <i class="fas fa-user-plus"></i><br>
                            Register New Staff
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('admin.manual_attendance_entry') }}" class="btn btn-success btn-block">
                            <i class="fas fa-edit"></i><br>
                            Manual Attendance
                        </a>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button type="button" class="btn btn-info btn-block" onclick="syncAttendance()">
                            <i class="fas fa-sync"></i><br>
                            Sync Device Data
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-warning btn-block">
                            <i class="fas fa-file-export"></i><br>
                            Generate Reports
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Check device status on page load
document.addEventListener('DOMContentLoaded', function() {
    checkDeviceStatus();
    // Auto-refresh device status every 30 seconds
    setInterval(checkDeviceStatus, 30000);
});

function checkDeviceStatus() {
    fetch('/api/device/status')
        .then(response => response.json())
        .then(data => {
            const statusText = document.getElementById('device-status-text');
            const alertElement = document.getElementById('device-status-alert');

            if (data.connected) {
                statusText.innerHTML = `
                    <span class="text-success">
                        <i class="fas fa-check-circle"></i> Connected to ESSL X990+ID
                        (${data.device_ip}:${data.device_port})
                    </span>
                `;
                alertElement.className = 'alert alert-success';
            } else {
                statusText.innerHTML = `
                    <span class="text-danger">
                        <i class="fas fa-times-circle"></i> Device not connected
                        (${data.device_ip}:${data.device_port})
                    </span>
                `;
                alertElement.className = 'alert alert-danger';

                if (data.error) {
                    statusText.innerHTML += `<br><small>Error: ${data.error}</small>`;
                }
            }
        })
        .catch(error => {
            console.error('Device status check failed:', error);
            document.getElementById('device-status-text').innerHTML = `
                <span class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i> Status check failed
                </span>
            `;
            document.getElementById('device-status-alert').className = 'alert alert-warning';
        });
}

function testDevice() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Testing...';
    btn.disabled = true;

    fetch('/api/device/test', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Device test successful!', 'success');
            checkDeviceStatus(); // Refresh status
        } else {
            showNotification('Device test failed: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Device test error:', error);
        showNotification('Device test error: ' + error.message, 'danger');
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function syncAttendance() {
    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><br>Syncing...';
    btn.disabled = true;

    // Make API call to sync attendance
    fetch('/api/sync-attendance', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(`Synced ${data.count} attendance records successfully!`, 'success');
            // Reload page to show updated data
            setTimeout(() => location.reload(), 1500);
        } else {
            showNotification('Sync failed: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Error syncing attendance data. Please try again.', 'danger');
    })
    .finally(() => {
        // Restore button state
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 80px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}
</script>
{% endblock %}
