#!/usr/bin/env python3
"""
Test script to verify the staff management system setup
"""

import sys
import os
from datetime import datetime, date, timedelta

def test_imports():
    """Test if all required modules can be imported"""
    print("Testing imports...")
    
    try:
        from app import create_app, db
        from app.models import User, Staff, Attendance, LeaveRequest, FingerprintTemplate, AttendanceLog
        from app.services.biometric_service import BiometricService, ESSLDevice
        print("✓ All imports successful")
        return True
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_app_creation():
    """Test if Flask app can be created"""
    print("Testing app creation...")
    
    try:
        from app import create_app
        app = create_app('testing')
        print("✓ Flask app created successfully")
        return True, app
    except Exception as e:
        print(f"✗ App creation error: {e}")
        return False, None

def test_database_models(app):
    """Test database models and relationships"""
    print("Testing database models...")
    
    try:
        with app.app_context():
            from app import db
            from app.models import User, Staff, Attendance, LeaveRequest
            
            # Create tables
            db.create_all()
            
            # Test User model
            user = User(username='testuser', email='<EMAIL>', role='staff')
            user.set_password('testpass')
            
            # Test Staff model
            staff = Staff(
                staff_id='TEST001',
                first_name='Test',
                last_name='User',
                email='<EMAIL>',
                department='IT',
                designation='Developer',
                date_of_joining=date.today()
            )
            
            # Test relationships
            staff.user = user
            
            # Test Attendance model
            attendance = Attendance(
                staff=staff,
                date=date.today(),
                check_in_time=datetime.now(),
                status='present'
            )
            
            # Test LeaveRequest model
            leave_request = LeaveRequest(
                staff=staff,
                leave_type='casual',
                start_date=date.today() + timedelta(days=1),
                end_date=date.today() + timedelta(days=2),
                total_days=2,
                reason='Test leave request'
            )
            
            print("✓ Database models work correctly")
            return True
            
    except Exception as e:
        print(f"✗ Database model error: {e}")
        return False

def test_routes(app):
    """Test if routes are accessible"""
    print("Testing routes...")
    
    try:
        with app.test_client() as client:
            # Test main routes
            response = client.get('/')
            assert response.status_code == 200
            
            response = client.get('/auth/login')
            assert response.status_code == 200
            
            response = client.get('/api/health')
            assert response.status_code == 200
            
            print("✓ Routes are accessible")
            return True
            
    except Exception as e:
        print(f"✗ Route testing error: {e}")
        return False

def test_biometric_service():
    """Test biometric service (without actual device)"""
    print("Testing biometric service...")
    
    try:
        from app.services.biometric_service import ESSLDevice
        
        # Test device class instantiation
        device = ESSLDevice('192.168.1.100', 4370, 0)
        
        # Test checksum calculation
        test_data = b'test data'
        checksum = device._calculate_checksum(test_data)
        assert isinstance(checksum, int)
        
        # Test header creation
        header = device._create_header(1000, test_data)
        assert len(header) >= 8  # Header should be at least 8 bytes
        
        print("✓ Biometric service classes work correctly")
        return True
        
    except Exception as e:
        print(f"✗ Biometric service error: {e}")
        return False

def test_configuration():
    """Test configuration loading"""
    print("Testing configuration...")
    
    try:
        from config import config, Config
        
        # Test config classes
        dev_config = config['development']
        prod_config = config['production']
        test_config = config['testing']
        
        # Test config attributes
        assert hasattr(Config, 'SECRET_KEY')
        assert hasattr(Config, 'SQLALCHEMY_DATABASE_URI')
        assert hasattr(Config, 'ESSL_DEVICE_IP')
        
        print("✓ Configuration works correctly")
        return True
        
    except Exception as e:
        print(f"✗ Configuration error: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("Staff Management System - Setup Test")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 6
    
    # Test 1: Imports
    if test_imports():
        tests_passed += 1
    
    # Test 2: Configuration
    if test_configuration():
        tests_passed += 1
    
    # Test 3: App creation
    app_success, app = test_app_creation()
    if app_success:
        tests_passed += 1
    
    # Test 4: Database models (only if app creation succeeded)
    if app_success and test_database_models(app):
        tests_passed += 1
    
    # Test 5: Routes (only if app creation succeeded)
    if app_success and test_routes(app):
        tests_passed += 1
    
    # Test 6: Biometric service
    if test_biometric_service():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✓ All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Copy .env.example to .env and configure your settings")
        print("2. Run: python init_db.py")
        print("3. Run: python app.py")
        print("4. Access the system at http://localhost:5000")
        return True
    else:
        print("✗ Some tests failed. Please check the errors above.")
        print("\nTroubleshooting:")
        print("1. Make sure all dependencies are installed: pip install -r requirements.txt")
        print("2. Check Python version (3.8+ required)")
        print("3. Verify all files are in the correct locations")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
