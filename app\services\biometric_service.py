"""
Biometric Device Integration Service
This service handles communication with ESSL X990+ID biometric devices
"""

import socket
import struct
import time
from datetime import datetime
from flask import current_app
from app import db
from app.models import AttendanceLog, FingerprintTemplate, Staff

class ESSLDevice:
    """ESSL Biometric Device Communication Class"""
    
    def __init__(self, ip, port=4370, password=0):
        self.ip = ip
        self.port = port
        self.password = password
        self.socket = None
        self.session_id = 0
        self.reply_id = 0
    
    def connect(self):
        """Connect to the biometric device"""
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            self.socket.settimeout(5)  # 5 second timeout for better responsiveness

            # Send connection request
            command = self._create_header(1000, b'')  # CMD_CONNECT
            self.socket.sendto(command, (self.ip, self.port))

            # Receive response
            data, addr = self.socket.recvfrom(1024)
            if len(data) >= 8:
                reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
                if command_id == 2000:  # CMD_ACK_OK
                    self.session_id = session_id
                    current_app.logger.info(f'Connected to device {self.ip}:{self.port} with session {session_id}')
                    return True
                else:
                    current_app.logger.warning(f'Device connection rejected with command {command_id}')
                    return False

            current_app.logger.warning(f'Invalid response from device {self.ip}:{self.port}')
            return False

        except socket.timeout:
            current_app.logger.error(f'Connection timeout to device {self.ip}:{self.port}')
            return False
        except socket.gaierror as e:
            current_app.logger.error(f'DNS resolution failed for {self.ip}: {str(e)}')
            return False
        except ConnectionRefusedError:
            current_app.logger.error(f'Connection refused by device {self.ip}:{self.port}')
            return False
        except Exception as e:
            current_app.logger.error(f'Failed to connect to device {self.ip}:{self.port}: {str(e)}')
            return False
    
    def disconnect(self):
        """Disconnect from the biometric device"""
        try:
            if self.socket:
                command = self._create_header(1001, b'')  # CMD_EXIT
                self.socket.sendto(command, (self.ip, self.port))
                self.socket.close()
                self.socket = None
                current_app.logger.info(f'Disconnected from device {self.ip}:{self.port}')
        except Exception as e:
            current_app.logger.error(f'Error disconnecting from device: {str(e)}')

    def get_device_info(self):
        """Get device information"""
        try:
            if not self.socket:
                if not self.connect():
                    return None

            # Send device info request
            command = self._create_header(11, b'')  # CMD_GET_TIME or similar
            self.socket.sendto(command, (self.ip, self.port))

            # Wait for response
            data, addr = self.socket.recvfrom(1024)
            if len(data) >= 8:
                reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])

                # Parse device info from response
                device_info = {
                    'model': 'ESSL X990+ID',
                    'firmware_version': 'Unknown',
                    'serial_number': 'Unknown',
                    'user_count': 0,
                    'fingerprint_count': 0,
                    'record_count': 0,
                    'device_time': datetime.now().isoformat()
                }

                return device_info

            return None

        except Exception as e:
            current_app.logger.error(f'Error getting device info: {str(e)}')
            return None
    
    def get_attendance_logs(self):
        """Retrieve attendance logs from the device"""
        try:
            if not self.socket:
                if not self.connect():
                    return []
            
            # Send command to get attendance logs
            command = self._create_header(13, b'')  # CMD_ATTLOG_RRQ
            self.socket.sendto(command, (self.ip, self.port))
            
            logs = []
            while True:
                try:
                    data, addr = self.socket.recvfrom(1024)
                    if len(data) < 8:
                        break
                    
                    # Parse header
                    reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
                    
                    if command_id == 13:  # CMD_ATTLOG_RRQ response
                        # Parse attendance data
                        payload = data[8:]
                        logs.extend(self._parse_attendance_data(payload))
                    elif command_id == 14:  # CMD_ATTLOG_RRQ end
                        break
                        
                except socket.timeout:
                    break
            
            return logs
            
        except Exception as e:
            current_app.logger.error(f'Error getting attendance logs: {str(e)}')
            return []
    
    def capture_fingerprint(self):
        """Capture fingerprint from device"""
        try:
            if not self.socket:
                if not self.connect():
                    return False, None

            # Send fingerprint capture command
            command = self._create_header(23, b'')  # CMD_CAPTURE_FINGER
            self.socket.sendto(command, (self.ip, self.port))

            # Wait for response with timeout
            start_time = time.time()
            while time.time() - start_time < 30:  # 30 second timeout
                try:
                    data, addr = self.socket.recvfrom(4096)
                    if len(data) >= 8:
                        reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])

                        if command_id == 2000:  # CMD_ACK_OK
                            # Extract template data from response
                            template_data = data[8:] if len(data) > 8 else b''
                            return True, template_data
                        elif command_id == 2001:  # CMD_ACK_ERROR
                            return False, None

                except socket.timeout:
                    continue

            return False, None

        except Exception as e:
            current_app.logger.error(f'Error capturing fingerprint: {str(e)}')
            return False, None

    def register_fingerprint(self, user_id, finger_id, template_data):
        """Register fingerprint template on the device"""
        try:
            if not self.socket:
                if not self.connect():
                    return False

            # Prepare fingerprint data
            fp_data = struct.pack('<HH', user_id, finger_id) + template_data

            # Send fingerprint registration command
            command = self._create_header(8, fp_data)  # CMD_USER_WRQ
            self.socket.sendto(command, (self.ip, self.port))

            # Wait for response
            data, addr = self.socket.recvfrom(1024)
            if len(data) >= 8:
                reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
                return command_id == 2000  # CMD_ACK_OK

            return False

        except Exception as e:
            current_app.logger.error(f'Error registering fingerprint: {str(e)}')
            return False
    
    def delete_user(self, user_id):
        """Delete user from the device"""
        try:
            if not self.socket:
                if not self.connect():
                    return False
            
            # Prepare user deletion data
            user_data = struct.pack('<H', user_id)
            
            # Send user deletion command
            command = self._create_header(18, user_data)  # CMD_DELETE_USER
            self.socket.sendto(command, (self.ip, self.port))
            
            # Wait for response
            data, addr = self.socket.recvfrom(1024)
            if len(data) >= 8:
                reply_id, command_id, checksum, session_id = struct.unpack('<HHHH', data[:8])
                return command_id == 2000  # CMD_ACK_OK
            
            return False
            
        except Exception as e:
            current_app.logger.error(f'Error deleting user: {str(e)}')
            return False
    
    def _create_header(self, command_id, data):
        """Create command header for device communication"""
        self.reply_id += 1
        if self.reply_id > 65535:
            self.reply_id = 1
        
        checksum = self._calculate_checksum(data)
        header = struct.pack('<HHHH', self.reply_id, command_id, checksum, self.session_id)
        return header + data
    
    def _calculate_checksum(self, data):
        """Calculate checksum for data"""
        checksum = 0
        for byte in data:
            checksum += byte
        return checksum & 0xFFFF
    
    def _parse_attendance_data(self, data):
        """Parse attendance data from device response"""
        logs = []
        
        # Each attendance record is typically 16 bytes
        record_size = 16
        for i in range(0, len(data), record_size):
            if i + record_size <= len(data):
                record = data[i:i + record_size]
                
                # Parse record (format may vary by device model)
                # This is a simplified parsing - adjust based on your device's format
                try:
                    user_id, verify_type, in_out_type, year, month, day, hour, minute, second = struct.unpack('<HBBBBBBBB', record[:9])
                    
                    # Create datetime
                    timestamp = datetime(2000 + year, month, day, hour, minute, second)
                    
                    # Determine punch type
                    punch_type = 'in' if in_out_type == 0 else 'out'
                    
                    logs.append({
                        'device_user_id': user_id,
                        'timestamp': timestamp,
                        'punch_type': punch_type,
                        'verify_type': verify_type
                    })
                    
                except struct.error:
                    continue
        
        return logs

class BiometricService:
    """Service class for biometric device operations"""
    
    @staticmethod
    def sync_attendance_from_device():
        """Sync attendance data from biometric device"""
        try:
            device_ip = current_app.config.get('ESSL_DEVICE_IP')
            device_port = current_app.config.get('ESSL_DEVICE_PORT', 4370)
            device_password = current_app.config.get('ESSL_DEVICE_PASSWORD', 0)
            
            device = ESSLDevice(device_ip, device_port, device_password)
            
            if device.connect():
                logs = device.get_attendance_logs()
                
                processed_count = 0
                for log_data in logs:
                    # Check if log already exists
                    existing_log = AttendanceLog.query.filter_by(
                        device_user_id=log_data['device_user_id'],
                        timestamp=log_data['timestamp']
                    ).first()
                    
                    if not existing_log:
                        # Create new attendance log
                        log_entry = AttendanceLog(
                            device_user_id=log_data['device_user_id'],
                            timestamp=log_data['timestamp'],
                            punch_type=log_data['punch_type'],
                            device_id=device_ip,
                            raw_data=str(log_data)
                        )
                        
                        db.session.add(log_entry)
                        processed_count += 1
                
                db.session.commit()
                device.disconnect()
                
                current_app.logger.info(f'Synced {processed_count} new attendance logs')
                return processed_count
            
            return 0
            
        except Exception as e:
            current_app.logger.error(f'Error syncing attendance from device: {str(e)}')
            return 0
    
    @staticmethod
    def capture_fingerprint_template():
        """Capture fingerprint template from biometric device"""
        try:
            device_ip = current_app.config.get('ESSL_DEVICE_IP')
            device_port = current_app.config.get('ESSL_DEVICE_PORT', 4370)
            device_password = current_app.config.get('ESSL_DEVICE_PASSWORD', 0)

            device = ESSLDevice(device_ip, device_port, device_password)

            if device.connect():
                success, template_data = device.capture_fingerprint()
                device.disconnect()

                if success:
                    return True, template_data
                else:
                    return False, None

            return False, None

        except Exception as e:
            current_app.logger.error(f'Error capturing fingerprint: {str(e)}')
            return False, None

    @staticmethod
    def register_staff_fingerprint(staff_id, finger_id, device_user_id):
        """Register staff fingerprint on biometric device"""
        try:
            staff = Staff.query.get(staff_id)
            if not staff:
                return False, "Staff not found"

            device_ip = current_app.config.get('ESSL_DEVICE_IP')
            device_port = current_app.config.get('ESSL_DEVICE_PORT', 4370)
            device_password = current_app.config.get('ESSL_DEVICE_PASSWORD', 0)

            device = ESSLDevice(device_ip, device_port, device_password)

            if device.connect():
                # First capture the fingerprint
                success, template_data = device.capture_fingerprint()

                if not success or not template_data:
                    device.disconnect()
                    return False, "Failed to capture fingerprint. Please try again."

                # Then register it on the device
                success = device.register_fingerprint(device_user_id, finger_id, template_data)

                if success:
                    # Save fingerprint template to database
                    fingerprint = FingerprintTemplate(
                        staff_id=staff_id,
                        finger_id=finger_id,
                        template_data=template_data,
                        device_user_id=device_user_id
                    )

                    db.session.add(fingerprint)
                    db.session.commit()

                device.disconnect()
                return success, "Fingerprint registered successfully" if success else "Failed to register fingerprint on device"

            return False, "Failed to connect to biometric device"

        except Exception as e:
            current_app.logger.error(f'Error registering fingerprint: {str(e)}')
            return False, str(e)
