<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% if title %}{{ title }} - {% endif %}Staff Management System</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #343a40;
        }
        .sidebar .nav-link {
            color: #adb5bd;
        }
        .sidebar .nav-link:hover {
            color: #fff;
        }
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        .main-content {
            margin-left: 0;
        }
        @media (min-width: 768px) {
            .main-content {
                margin-left: 250px;
            }
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card-stats {
            border-left: 4px solid #007bff;
        }
        .card-stats.success {
            border-left-color: #28a745;
        }
        .card-stats.warning {
            border-left-color: #ffc107;
        }
        .card-stats.danger {
            border-left-color: #dc3545;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('main.index') }}">
                <i class="fas fa-users"></i> Staff Management System
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    {% if current_user.is_authenticated %}
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                                <i class="fas fa-user"></i> {{ current_user.username }}
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="{{ url_for('auth.change_password') }}">
                                    <i class="fas fa-key"></i> Change Password
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="{{ url_for('auth.logout') }}">
                                    <i class="fas fa-sign-out-alt"></i> Logout
                                </a></li>
                            </ul>
                        </li>
                    {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="{{ url_for('auth.login') }}">
                                <i class="fas fa-sign-in-alt"></i> Login
                            </a>
                        </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid" style="margin-top: 56px;">
        <div class="row">
            {% if current_user.is_authenticated %}
                <!-- Sidebar -->
                <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="position-sticky pt-3">
                        <ul class="nav flex-column">
                            {% if current_user.is_admin() %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('admin.dashboard') }}">
                                        <i class="fas fa-tachometer-alt"></i> Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('admin.staff_list') }}">
                                        <i class="fas fa-users"></i> Staff Management
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('admin.register_staff') }}">
                                        <i class="fas fa-user-plus"></i> Register Staff
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('admin.attendance_list') }}">
                                        <i class="fas fa-calendar-check"></i> Attendance
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('admin.manual_attendance_entry') }}">
                                        <i class="fas fa-edit"></i> Manual Entry
                                    </a>
                                </li>
                            {% else %}
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('staff.dashboard') }}">
                                        <i class="fas fa-tachometer-alt"></i> Dashboard
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('staff.attendance_history') }}">
                                        <i class="fas fa-calendar-check"></i> My Attendance
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('staff.request_leave') }}">
                                        <i class="fas fa-calendar-times"></i> Request Leave
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('staff.leave_history') }}">
                                        <i class="fas fa-history"></i> Leave History
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a class="nav-link" href="{{ url_for('staff.profile') }}">
                                        <i class="fas fa-user"></i> My Profile
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </div>
                </nav>
            {% endif %}

            <!-- Main content -->
            <main class="{% if current_user.is_authenticated %}col-md-9 ms-sm-auto col-lg-10 px-md-4{% else %}col-12{% endif %} main-content">
                <!-- Flash messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="mt-3">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                    {{ message }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}

                <!-- Page content -->
                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    {% block scripts %}{% endblock %}
</body>
</html>
