{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-edit"></i> Manual Attendance Entry</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('admin.attendance_list') }}" class="btn btn-sm btn-outline-secondary">
                <i class="fas fa-arrow-left"></i> Back to Attendance
            </a>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-calendar-plus"></i> Add Attendance Record</h5>
            </div>
            <div class="card-body">
                <form method="POST">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.staff_id.label(class="form-label") }}
                            {{ form.staff_id(class="form-select") }}
                            {% if form.staff_id.errors %}
                                <div class="text-danger">
                                    {% for error in form.staff_id.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.date.label(class="form-label") }}
                            {{ form.date(class="form-control") }}
                            {% if form.date.errors %}
                                <div class="text-danger">
                                    {% for error in form.date.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.check_in_time.label(class="form-label") }}
                            {{ form.check_in_time(class="form-control", placeholder="09:00") }}
                            {% if form.check_in_time.errors %}
                                <div class="text-danger">
                                    {% for error in form.check_in_time.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Format: HH:MM (24-hour)</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.check_out_time.label(class="form-label") }}
                            {{ form.check_out_time(class="form-control", placeholder="17:00") }}
                            {% if form.check_out_time.errors %}
                                <div class="text-danger">
                                    {% for error in form.check_out_time.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Format: HH:MM (24-hour)</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.break_start_time.label(class="form-label") }}
                            {{ form.break_start_time(class="form-control", placeholder="12:00") }}
                            {% if form.break_start_time.errors %}
                                <div class="text-danger">
                                    {% for error in form.break_start_time.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Optional - Format: HH:MM</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            {{ form.break_end_time.label(class="form-label") }}
                            {{ form.break_end_time(class="form-control", placeholder="13:00") }}
                            {% if form.break_end_time.errors %}
                                <div class="text-danger">
                                    {% for error in form.break_end_time.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                            <small class="form-text text-muted">Optional - Format: HH:MM</small>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            {{ form.status.label(class="form-label") }}
                            {{ form.status(class="form-select") }}
                            {% if form.status.errors %}
                                <div class="text-danger">
                                    {% for error in form.status.errors %}
                                        <small>{{ error }}</small>
                                    {% endfor %}
                                </div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows="3", placeholder="Optional notes about this attendance entry...") }}
                        {% if form.notes.errors %}
                            <div class="text-danger">
                                {% for error in form.notes.errors %}
                                    <small>{{ error }}</small>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="{{ url_for('admin.attendance_list') }}" class="btn btn-secondary me-md-2">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        {{ form.submit(class="btn btn-primary") }}
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card shadow">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-info-circle"></i> Instructions</h5>
            </div>
            <div class="card-body">
                <h6>Manual Entry Guidelines:</h6>
                <ul class="small">
                    <li>Select the staff member from the dropdown</li>
                    <li>Choose the date for the attendance record</li>
                    <li>Enter times in 24-hour format (HH:MM)</li>
                    <li>Break times are optional</li>
                    <li>Select appropriate status based on attendance</li>
                </ul>
                
                <hr>
                
                <h6>Status Definitions:</h6>
                <ul class="small">
                    <li><strong>Present:</strong> Normal working day</li>
                    <li><strong>Absent:</strong> Did not attend work</li>
                    <li><strong>Late:</strong> Arrived after scheduled time</li>
                    <li><strong>Half Day:</strong> Worked partial hours</li>
                    <li><strong>Leave:</strong> On approved leave</li>
                </ul>
                
                <hr>
                
                <div class="alert alert-info small">
                    <i class="fas fa-lightbulb"></i>
                    <strong>Tip:</strong> Total working hours will be calculated automatically based on check-in/out times and break duration.
                </div>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="card shadow mt-4">
            <div class="card-header">
                <h5 class="mb-0"><i class="fas fa-bolt"></i> Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button type="button" class="btn btn-outline-primary btn-sm" onclick="setCurrentTime('check_in')">
                        <i class="fas fa-clock"></i> Set Current Time (Check In)
                    </button>
                    <button type="button" class="btn btn-outline-warning btn-sm" onclick="setCurrentTime('check_out')">
                        <i class="fas fa-clock"></i> Set Current Time (Check Out)
                    </button>
                    <button type="button" class="btn btn-outline-info btn-sm" onclick="setStandardHours()">
                        <i class="fas fa-business-time"></i> Set Standard Hours (9-5)
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function setCurrentTime(field) {
    const now = new Date();
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const timeString = `${hours}:${minutes}`;
    
    if (field === 'check_in') {
        document.getElementById('check_in_time').value = timeString;
    } else if (field === 'check_out') {
        document.getElementById('check_out_time').value = timeString;
    }
}

function setStandardHours() {
    document.getElementById('check_in_time').value = '09:00';
    document.getElementById('check_out_time').value = '17:00';
    document.getElementById('break_start_time').value = '12:00';
    document.getElementById('break_end_time').value = '13:00';
}

// Auto-calculate total hours when times are entered
document.addEventListener('DOMContentLoaded', function() {
    const checkInField = document.getElementById('check_in_time');
    const checkOutField = document.getElementById('check_out_time');
    const breakStartField = document.getElementById('break_start_time');
    const breakEndField = document.getElementById('break_end_time');
    
    function calculateHours() {
        const checkIn = checkInField.value;
        const checkOut = checkOutField.value;
        const breakStart = breakStartField.value;
        const breakEnd = breakEndField.value;
        
        if (checkIn && checkOut) {
            const checkInTime = new Date(`2000-01-01 ${checkIn}`);
            const checkOutTime = new Date(`2000-01-01 ${checkOut}`);
            
            let totalMinutes = (checkOutTime - checkInTime) / (1000 * 60);
            
            if (breakStart && breakEnd) {
                const breakStartTime = new Date(`2000-01-01 ${breakStart}`);
                const breakEndTime = new Date(`2000-01-01 ${breakEnd}`);
                const breakMinutes = (breakEndTime - breakStartTime) / (1000 * 60);
                totalMinutes -= breakMinutes;
            }
            
            const totalHours = (totalMinutes / 60).toFixed(2);
            
            // You could display this somewhere if needed
            console.log(`Total working hours: ${totalHours}`);
        }
    }
    
    [checkInField, checkOutField, breakStartField, breakEndField].forEach(field => {
        field.addEventListener('change', calculateHours);
    });
});
</script>
{% endblock %}
