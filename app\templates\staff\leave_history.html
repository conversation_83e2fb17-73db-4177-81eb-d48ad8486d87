{% extends "base.html" %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="fas fa-history"></i> My Leave History</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('staff.request_leave') }}" class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i> Request Leave
            </a>
        </div>
    </div>
</div>

<!-- Leave Summary -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card border-left-primary shadow h-100 py-2 card-stats">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                            Total Requests
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ leave_requests.total }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-times fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-warning shadow h-100 py-2 card-stats warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                            Pending
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ leave_requests.items | selectattr('status', 'equalto', 'pending') | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-success shadow h-100 py-2 card-stats success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                            Approved
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ leave_requests.items | selectattr('status', 'equalto', 'approved') | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3 mb-3">
        <div class="card border-left-danger shadow h-100 py-2 card-stats danger">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                            Rejected
                        </div>
                        <div class="h5 mb-0 font-weight-bold text-gray-800">
                            {{ leave_requests.items | selectattr('status', 'equalto', 'rejected') | list | length }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-times-circle fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Leave Requests -->
<div class="card shadow">
    <div class="card-header">
        <h5 class="mb-0"><i class="fas fa-list"></i> Leave Requests</h5>
    </div>
    <div class="card-body p-0">
        {% if leave_requests.items %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Request Date</th>
                            <th>Leave Type</th>
                            <th>Duration</th>
                            <th>Days</th>
                            <th>Status</th>
                            <th>Approved By</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for leave in leave_requests.items %}
                        <tr>
                            <td>
                                <strong>{{ leave.applied_date.strftime('%m/%d/%Y') }}</strong><br>
                                <small class="text-muted">{{ leave.applied_date.strftime('%H:%M') }}</small>
                            </td>
                            <td>
                                <span class="badge bg-info">{{ leave.leave_type.title() }}</span>
                            </td>
                            <td>
                                <strong>{{ leave.start_date.strftime('%m/%d/%Y') }}</strong><br>
                                <small class="text-muted">to {{ leave.end_date.strftime('%m/%d/%Y') }}</small>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ leave.total_days }} day{{ 's' if leave.total_days != 1 else '' }}</span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'warning' if leave.status == 'pending' else 'success' if leave.status == 'approved' else 'danger' }}">
                                    {{ leave.status.title() }}
                                </span>
                                {% if leave.status == 'pending' %}
                                    <br><small class="text-muted">Awaiting approval</small>
                                {% elif leave.approved_date %}
                                    <br><small class="text-muted">{{ leave.approved_date.strftime('%m/%d/%Y') }}</small>
                                {% endif %}
                            </td>
                            <td>
                                {% if leave.approver %}
                                    <small>{{ leave.approver.username }}</small>
                                {% else %}
                                    <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td>
                                <button type="button" class="btn btn-sm btn-outline-primary" 
                                        onclick="viewLeaveDetails({{ leave.id }})" title="View Details">
                                    <i class="fas fa-eye"></i>
                                </button>
                                {% if leave.status == 'pending' %}
                                    <button type="button" class="btn btn-sm btn-outline-danger" 
                                            onclick="cancelLeave({{ leave.id }})" title="Cancel Request">
                                        <i class="fas fa-times"></i>
                                    </button>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        {% else %}
            <div class="text-center py-5">
                <i class="fas fa-calendar-check fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">No leave requests found</h5>
                <p class="text-muted">You haven't submitted any leave requests yet.</p>
                <a href="{{ url_for('staff.request_leave') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Submit Your First Request
                </a>
            </div>
        {% endif %}
    </div>
</div>

<!-- Pagination -->
{% if leave_requests.pages > 1 %}
<nav aria-label="Leave requests pagination" class="mt-4">
    <ul class="pagination justify-content-center">
        {% if leave_requests.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('staff.leave_history', page=leave_requests.prev_num) }}">
                    <i class="fas fa-chevron-left"></i> Previous
                </a>
            </li>
        {% endif %}
        
        {% for page_num in leave_requests.iter_pages() %}
            {% if page_num %}
                {% if page_num != leave_requests.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('staff.leave_history', page=page_num) }}">
                            {{ page_num }}
                        </a>
                    </li>
                {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                {% endif %}
            {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
            {% endif %}
        {% endfor %}
        
        {% if leave_requests.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('staff.leave_history', page=leave_requests.next_num) }}">
                    Next <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        {% endif %}
    </ul>
</nav>
{% endif %}

<!-- Leave Details Modal -->
<div class="modal fade" id="leaveDetailsModal" tabindex="-1" aria-labelledby="leaveDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="leaveDetailsModalLabel">
                    <i class="fas fa-calendar-times"></i> Leave Request Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="leaveDetailsContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
function viewLeaveDetails(leaveId) {
    // Find the leave request in the current data
    const leaveRequests = {{ leave_requests.items | tojson }};
    const leave = leaveRequests.find(l => l.id === leaveId);
    
    if (leave) {
        const content = `
            <div class="row">
                <div class="col-md-6">
                    <h6>Leave Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Type:</strong></td><td><span class="badge bg-info">${leave.leave_type}</span></td></tr>
                        <tr><td><strong>Start Date:</strong></td><td>${new Date(leave.start_date).toLocaleDateString()}</td></tr>
                        <tr><td><strong>End Date:</strong></td><td>${new Date(leave.end_date).toLocaleDateString()}</td></tr>
                        <tr><td><strong>Total Days:</strong></td><td>${leave.total_days}</td></tr>
                        <tr><td><strong>Applied Date:</strong></td><td>${new Date(leave.applied_date).toLocaleDateString()}</td></tr>
                    </table>
                </div>
                <div class="col-md-6">
                    <h6>Status Information</h6>
                    <table class="table table-sm">
                        <tr><td><strong>Status:</strong></td><td><span class="badge bg-${leave.status === 'pending' ? 'warning' : leave.status === 'approved' ? 'success' : 'danger'}">${leave.status}</span></td></tr>
                        ${leave.approved_date ? `<tr><td><strong>Approved Date:</strong></td><td>${new Date(leave.approved_date).toLocaleDateString()}</td></tr>` : ''}
                        ${leave.approver ? `<tr><td><strong>Approved By:</strong></td><td>${leave.approver.username}</td></tr>` : ''}
                    </table>
                </div>
            </div>
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Reason</h6>
                    <div class="border p-3 bg-light rounded">
                        ${leave.reason}
                    </div>
                </div>
            </div>
            ${leave.admin_comments ? `
            <div class="row mt-3">
                <div class="col-12">
                    <h6>Admin Comments</h6>
                    <div class="border p-3 bg-light rounded">
                        ${leave.admin_comments}
                    </div>
                </div>
            </div>
            ` : ''}
        `;
        
        document.getElementById('leaveDetailsContent').innerHTML = content;
        new bootstrap.Modal(document.getElementById('leaveDetailsModal')).show();
    }
}

function cancelLeave(leaveId) {
    if (confirm('Are you sure you want to cancel this leave request?')) {
        // Implement cancel functionality
        alert('Cancel leave request functionality would be implemented here.');
    }
}
</script>
{% endblock %}
